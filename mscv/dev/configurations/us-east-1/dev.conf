vendor.definitions = [
 {
    name = "trice"
    type = "http"
    builders {
      request = "http_request_builder"
      response = "http_response_builder"
    }
    connection {
      endpoint = "https://api-sandbox.trice.co"
      uri = ""
      authorization {
        endpoint = "https://api-sandbox.trice.co"
        uri = "/auth/v1/oauth2/token"
        username = "ENC(G0o/CiSsejTXOQtimugixe7ObyLI+khNnJnTN/h43VRZuXhG35ixe4Yw3w722ypqV6/XjC/DrB87ljwYcHBG6zk61eo=)"
        password = "ENC(U4nj1xfIP8HGdziBiDvtyoEThKHlB62cIiXIk1jzMrkWh9cxCc3XHYig)"
      }
      useNewHttpConnectionForEachRequest = true
      connect.timeout = 4000
      read.timeout = 4000
      response.timeout = 4000
      ssl.enabled = false
    }
    configs {
      default.sla.timeout = 4000
      default.template = "default_verify"
      default.header.template = "trice.header.verify"
      trice.enrollment.id = "ENC(PIeuaYyEDHPjC915q31hI0oQscFPPVpz3NAY3SMNQ+Cg7smB5Xt1bkO3GzpE++y1foNbgsrGk7eDnHY9notksJwr7D8=)"
      trice.wait.for.header.timeout.millis = 2500
    }
    dry.run.enabled = true
    tp.audit.service.id = 222
  },
 {
             name = "bnyvl"
             type = "http"
             builders {
               request = "http_request_builder"
               response = "http_response_builder"
             }
             connection {
               endpoint = "https://apigateway.qa.bnymellon.com"
               uri = ""
               authorization {
                 endpoint = "https://apigatewayb2b.qa.bnymellon.com"
                 uri = "/token"
               }
               useNewHttpConnectionForEachRequest = true
               connect.timeout = 4000
               read.timeout = 4000
               response.timeout = 4000
               ssl.enabled = true
               aws.secret.id = "ai-bnymellon-service/dev/bnymellon-vendor-secretkey"
             }
             configs {
               default.sla.timeout = 4000
               default.template = "bnyvl.body.validate"
               default.header.template = "bnyvl.header.validate"
               bnyvl.aws.secret.id = "ai-bnymellon-service/dev/bnymellon-vendor-secretkey"
               bnyvl.auth.grant.type = "client_cert"
             }
             dry.run.enabled = true
             tp.audit.service.id = 222
           },
        {
          name = "mbtvl"
          type = "http"
          builders {
            request = "http_request_builder"
            response = "http_response_builder"
          }
          connection {
            endpoint = "https://apitest.microbilt.com"
            uri = ""
            authorization {
              endpoint = "https://apitest.microbilt.com"
              uri = "/OAuth/Token"
            }
            useNewHttpConnectionForEachRequest = true
            connect.timeout = 4000
            read.timeout = 4000
            response.timeout = 4000
            ssl.enabled = false

          }
          configs {
            default.sla.timeout = 4000
            default.template = "mbtbl.body.validate"
            default.header.template = "mbtvl.header.validate"
            mbtvl.aws.secret.id = "ai-microbilt-service/dev/microbilt-vendor-secret"
            mbtvl.rulenum = "100"
          }
          dry.run.enabled = true
          tp.audit.service.id = 222
        },
        {
                  name = "socvl"
                  type = "http"
                  builders {
                    request = "http_request_builder"
                    response = "http_response_builder"
                  }
                  connection {
                    endpoint = "http://id-resolution-service"
                    uri = ""
                    useNewHttpConnectionForEachRequest = true
                    connect.timeout = 4000
                    read.timeout = 4000
                    response.timeout = 4000
                    ssl.enabled = false

                  }
                  configs {
                      default.sla.timeout = 4000
                      default.template = "socvl.body.validate"
                      default.header.template = "socvl.header.validate"
                      #showDocs display the returned documents.  Setting to false is only recommended during code development and debugging
                      idResolution.staticApiParams.showDocs = true
                      idResolution.staticApiParams.displayMethod = "KYC+"
                      idResolution.staticApiParams.searchBySoundex = false
                      idResolution.staticApiParams.fuzzyDobSearch = false
                      idResolution.staticApiParams.compareMethod = "V5"
                      idResolution.staticApiParams.apiVersion = "V5"
                      idResolution.staticApiParams.thresholdScore = 0.2
                      idResolution.staticApiParams.maxResultRecords = 20
                      idResolution.staticApiParams.matchLevel = 3
                      idResolution.staticApiParams.showIndexes = true
                      #connectEnv value represents build table of SocureID
                      idResolution.staticApiParams.connectEnv = "2404r4"
                  }
                  dry.run.enabled = true
                  tp.audit.service.id = 222
        },
        {
                  name = "vrval"
                  type = "http"
                  builders {
                    request = "http_request_builder"
                    response = "http_response_builder"
                  }
                  connection {
                    endpoint = "http://kyc-search-service"
                    uri = ""
                    useNewHttpConnectionForEachRequest = true
                    connect.timeout = 4000
                    read.timeout = 4000
                    response.timeout = 4000
                    ssl.enabled = false

                  }
                  configs {
                    default.sla.timeout = 4000
                    default.template = "vrval.body.validate"
                    default.header.template = "vrval.header.validate"
                  }
                  dry.run.enabled = true
                  tp.audit.service.id = 222
                }
]

dynamo {
  max.concurrency = 100
  region = "us-east-1"
}
socure.kms.region = "us-east-1"

transaction-auditing {
  threadpool {
    poolSize = 30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region = us-east-1
        transaction {
          queueName = transaction-auditing-dev
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = third-party-transaction-auditing-dev
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region = us-west-2
        transaction {
          queueName = transaction-auditing-dev
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = third-party-transaction-auditing-dev
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region = us-east-2
        transaction {
          queueName = transaction-auditing-dev
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = third-party-transaction-auditing-dev
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder = "sqs-storage-dev-764009278656-us-east-1"
      }
      third-party {
        region = us-east-1
        bucket = "thirdparty-stats-dev-764009278656-us-east-1"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}
