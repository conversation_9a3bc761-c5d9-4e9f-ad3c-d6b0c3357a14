vendor.definitions = [
  {
    name = "trice"
    type = "http"
    builders {
      request = "http_request_builder"
      response = "http_response_builder"
    }
    connection {
      endpoint = "https://api.trice.co"
      uri = ""
      authorization {
        endpoint = "https://api.trice.co"
        uri = "/auth/v1/oauth2/token"
        username = "ENC(iRdim4A/Hl4wWnIYemPhWLEFnSrMCR1z/w6xtxELrc+/FTaXAKHEIqmeiM1aWQuUuXyhkAo3QHuo552MC1Hfc1b8fIE=)"
        password = "ENC(sc3yYCkC/9CmyPLkIONkara37vY4hM+vyWLT66DAjWwGWzo8LbQ3/hH9brMHbCnl)"
      }
      useNewHttpConnectionForEachRequest = true
      connect.timeout = 5000
      read.timeout = 5000
      response.timeout = 5000
      ssl.enabled = false
    }
    configs {
      default.sla.timeout = 5000
      default.template = "default_verify"
      default.header.template = "trice.header.verify"
      trice.enrollment.id = "ENC(IFU0Bu4xR7M3tpStpjKeIq7Odi41LVPAcT9CK41ITZ3bI7jOmCN0Mo5JG4fSZp/cTuyN5OijTvoS4EMSLt2nrkZu7BU=)"
      trice.wait.for.header.timeout.millis = 2500
    }
    dry.run.enabled = true
    tp.audit.service.id = 222
  },
    {
                name = "bnyvl"
                type = "http"
                builders {
                  request = "http_request_builder"
                  response = "http_response_builder"
                }
                connection {
                  endpoint = "https://apigateway.bnymellon.com"
                  uri = ""
                  authorization {
                    endpoint = "https://apigatewayb2b.bnymellon.com"
                    uri = "/token"
                  }
                  useNewHttpConnectionForEachRequest = true
                  connect.timeout = 4000
                  read.timeout = 4000
                  response.timeout = 4000
                  ssl.enabled = true
                  aws.secret.id = "ai-bnymellon-service/ds/bnymellon-vendor-secretkey"
                }
                configs {
                  default.sla.timeout = 4000
                  default.template = "bnyvl.body.validate"
                  default.header.template = "bnyvl.header.validate"
                  bnyvl.aws.secret.id = "ai-bnymellon-service/ds/bnymellon-vendor-secretkey"
                  bnyvl.auth.grant.type = "client_cert"
                }
                dry.run.enabled = true
                tp.audit.service.id = 222
              },
         {
           name = "mbtvl"
           type = "http"
           builders {
             request = "http_request_builder"
             response = "http_response_builder"
           }
           connection {
             endpoint = "https://api.microbilt.com"
             uri = ""
             authorization {
               endpoint = "https://api.microbilt.com"
               uri = "/OAuth/Token"
             }
             useNewHttpConnectionForEachRequest = true
             connect.timeout = 4000
             read.timeout = 4000
             response.timeout = 4000
             ssl.enabled = false

           }
           configs {
             default.sla.timeout = 4000
             default.template = "mbtbl.body.validate"
             default.header.template = "mbtvl.header.validate"
             mbtvl.aws.secret.id = "ai-microbilt-service/ds/microbilt-vendor-secret"
             mbtvl.rulenum = "5000"
           }
           dry.run.enabled = true
           tp.audit.service.id = 222
         },
         {
                           name = "socvl"
                           type = "http"
                           builders {
                             request = "http_request_builder"
                             response = "http_response_builder"
                           }
                           connection {
                             endpoint = "http://idplus-service"
                             uri = ""
                             authorization {
                               endpoint = "https://apitest.microbilt.com"
                               uri = "/OAuth/Token"
                             }
                             useNewHttpConnectionForEachRequest = true
                             connect.timeout = 4000
                             read.timeout = 4000
                             response.timeout = 4000
                             ssl.enabled = false

                           }
                           configs {
                               staticApiParams.showDocs = true
                               staticApiParams.displayMethod = "KYC+"
                               staticApiParams.searchBySoundex = false
                               staticApiParams.fuzzyDobSearch = false
                               staticApiParams.compareMethod = "V5"
                               staticApiParams.apiVersion = "V5"
                               staticApiParams.thresholdScore = 0.2
                               staticApiParams.maxResultRecords = 20
                               staticApiParams.matchLevel = 3
                               staticApiParams.showIndexes = true
                               #connectEnv value represents build table of SocureID
                               staticApiParams.connectEnv = "2404r4"
                           }
                           dry.run.enabled = true
                           tp.audit.service.id = 222
                 },
                 {
                  name = "vrval"
                  type = "http"
                  builders {
                    request = "http_request_builder"
                    response = "http_response_builder"
                  }
                  connection {
                    endpoint = "http://kyc-search-service"
                    uri = ""
                    useNewHttpConnectionForEachRequest = true
                    connect.timeout = 4000
                    read.timeout = 4000
                    response.timeout = 4000
                    ssl.enabled = false

                  }
                  configs {
                    default.sla.timeout = 4000
                    default.template = "vrval.body.validate"
                    default.header.template = "vrval.header.validate"
                  }
                  dry.run.enabled = true
                  tp.audit.service.id = 222
                }
]

dynamo {
  max.concurrency = 100
  region = "us-east-1"
}
socure.kms.region = "us-east-1"

transaction-auditing {
  threadpool {
    poolSize = 30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region = us-east-1
        transaction {
          queueName = transaction-auditing-ds
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = third-party-transaction-auditing-ds
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region = us-west-2
        transaction {
          queueName = transaction-auditing-ds
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = third-party-transaction-auditing-ds
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region = us-east-2
        transaction {
          queueName = transaction-auditing-ds
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = third-party-transaction-auditing-ds
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder = "sqs-storage-ds"
      }
      third-party {
        region = us-east-1
        bucket = "thirdparty-stats-ds"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}
