package me.socure.dynamo.ingestion.service;

import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.common.dynamo.DynamoIngestionRequest;
import me.socure.ai.vendor.common.dynamo.DynamoIngestionResponse;
import me.socure.ai.vendor.common.exception.DynamoIngestionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient;
import software.amazon.awssdk.services.dynamodb.model.*;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
@Log4j2
public class DynamoDBService {
    @Autowired
    DynamoDbAsyncClient dynamoDbAsyncClient;

    public DynamoIngestionResponse importTable(DynamoIngestionRequest dynamoIngestionRequest) throws DynamoIngestionException {

        log.info("INITIATING Dynamo Ingestion for tableName = "+dynamoIngestionRequest.getTableName()+
                " triggered by userName = "+dynamoIngestionRequest.getUserName());
        ImportTableRequest importTableRequest = getImportTableRequest(dynamoIngestionRequest);

        try {
            CompletableFuture<ImportTableResponse> importTableResponseFuture = dynamoDbAsyncClient.importTable(importTableRequest);
            log.info("Dynamo Ingestion IN_PROGRESS for tableName = "+dynamoIngestionRequest.getTableName()+" triggered by userName = "+dynamoIngestionRequest.getUserName());
            ImportTableResponse importTableResponse = importTableResponseFuture.get();
            DynamoIngestionResponse dynamoIngestionResponse = new DynamoIngestionResponse();
            dynamoIngestionResponse.setImportArn(importTableResponse.importTableDescription().importArn());
            dynamoIngestionResponse.setImportStatus(importTableResponse.importTableDescription().importStatusAsString());
            return dynamoIngestionResponse;
        } catch (Exception ex) {
            log.error("Dynamo Ingestion FAILED for tableName = "+dynamoIngestionRequest.getTableName()+
                    " triggered by userName = "+dynamoIngestionRequest.getUserName());
            log.error(ex.getStackTrace());
            throw new DynamoIngestionException("Dynamo Ingestion Failed with message : "+ex.getMessage());
        }
    }





    private List<GlobalSecondaryIndex> getGlobalSecondaryIndexList(DynamoIngestionRequest dynamoIngestionRequest) {
        List<GlobalSecondaryIndex> globalSecondaryIndexList =
                dynamoIngestionRequest.getGlobalSecondaryIndexes().stream().map(gsi -> {
                    Projection projection;
                    if(gsi.getNonKeyAttributes() != null && gsi.getNonKeyAttributes().size() > 0){
                        projection = Projection
                                .builder()
                                .projectionType(gsi.getProjection())
                                .nonKeyAttributes(gsi.getNonKeyAttributes())
                                .build();
                    } else {
                        projection = Projection
                                .builder()
                                .projectionType(gsi.getProjection())
                                .build();
                    }
                    List<KeySchemaElement> keySchemaElementList1 = new ArrayList<>();

                    keySchemaElementList1.add(KeySchemaElement
                            .builder()
                            .attributeName(gsi.getPrimaryKey())
                            .keyType(KeyType.HASH)
                            .build());

                    if(gsi.getSortKey() != null && !gsi.getSortKey().isEmpty() ) {
                        keySchemaElementList1.add(KeySchemaElement
                                .builder()
                                .attributeName(gsi.getSortKey())
                                .keyType(KeyType.RANGE)
                                .build());
                    }

                    return GlobalSecondaryIndex
                            .builder()
                            .indexName(gsi.getIndexName())
                            .keySchema(keySchemaElementList1)
                            .projection(projection)
                            .build();

                }).collect(Collectors.toList());
        return globalSecondaryIndexList;
    }

    private ImportTableRequest getImportTableRequest(DynamoIngestionRequest dynamoIngestionRequest){
        return ImportTableRequest
                .builder()
                .inputFormat(dynamoIngestionRequest.getInputFormat())
                .inputCompressionType(dynamoIngestionRequest.getInputCompressionType())
                .s3BucketSource(getS3BucketSource(dynamoIngestionRequest))
                .tableCreationParameters(getTableCreationParameters(dynamoIngestionRequest))
                .build();
    }

    private S3BucketSource getS3BucketSource(DynamoIngestionRequest dynamoIngestionRequest) {
        return S3BucketSource
                .builder()
                .s3Bucket(dynamoIngestionRequest.getS3Bucket())
                .s3BucketOwner(dynamoIngestionRequest.getS3BucketOwner())
                .s3KeyPrefix(dynamoIngestionRequest.getS3KeyPrefix())
                .build();
    }

    private TableCreationParameters getTableCreationParameters(DynamoIngestionRequest dynamoIngestionRequest) {

        List<String> keys = new ArrayList<>();
        List<KeySchemaElement> keySchemaElementList = new ArrayList<>();

        keys.add(dynamoIngestionRequest.getPrimaryKey());
        KeySchemaElement keySchemaElement = KeySchemaElement
                .builder()
                .attributeName(dynamoIngestionRequest.getPrimaryKey())
                .keyType(KeyType.HASH)
                .build();

        keySchemaElementList.add(keySchemaElement);

        if (dynamoIngestionRequest.getSortKey() != null && !dynamoIngestionRequest.getSortKey().isEmpty()){
            keys.add(dynamoIngestionRequest.getSortKey());
            keySchemaElementList.add(KeySchemaElement
                    .builder()
                    .attributeName(dynamoIngestionRequest.getSortKey())
                    .keyType(KeyType.RANGE)
                    .build());
        }
        if(dynamoIngestionRequest.getOtherKeys() != null && dynamoIngestionRequest.getOtherKeys().size() > 0) {
            keys.addAll(dynamoIngestionRequest.getOtherKeys());
        }

        List<AttributeDefinition> attributeDefinitions = keys.stream().map(k ->
                AttributeDefinition.builder().attributeName(k).attributeType("S").build()
        ).collect(Collectors.toList());


        TableCreationParameters.Builder tableCreationParametersBuilder = TableCreationParameters.builder()
                .tableName(dynamoIngestionRequest.getTableName())
                .billingMode(dynamoIngestionRequest.getBillingMode())
                .attributeDefinitions(attributeDefinitions)
                .keySchema(keySchemaElementList);

        if(dynamoIngestionRequest.getGlobalSecondaryIndexes() != null &&
                dynamoIngestionRequest.getGlobalSecondaryIndexes().size() > 0) {
            tableCreationParametersBuilder.globalSecondaryIndexes(getGlobalSecondaryIndexList(dynamoIngestionRequest));
        }

        if(dynamoIngestionRequest.getKmsKey() != null && !dynamoIngestionRequest.getKmsKey().isEmpty()) {
            tableCreationParametersBuilder.sseSpecification(SSESpecification
                    .builder()
                    .sseType(SSEType.KMS)
                    .enabled(true)
                    .kmsMasterKeyId(dynamoIngestionRequest.getKmsKey())
                    .build()
            );
        }
        return tableCreationParametersBuilder.build();
    }
}
