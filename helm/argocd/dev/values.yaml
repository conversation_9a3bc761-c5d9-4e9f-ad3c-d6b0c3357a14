# this helm file (values.yaml) is a dummy file. this will be overridden and the helm chart in argocd-dev only will be used
image:
  repository: fips-registry.us-east-1.build.socure.link/ai/ai-vendor-service
  tag: latest

serviceAccount:
  name: "ai-vendor-service-dev"
  annotations: 
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/eks-irsa-92ed150-dev-44bfbb99"

application:
  env:
    CONFIGURATION_NAME: "ai-vendor-service"
    CONFIGURATION_VERSION: mscv_f369d9b3
    JAVA_TOOL_OPTIONS: "-XX:InitialRAMPercentage=50.0 -XX:MaxRAMPercentage=85.0 -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:NewRatio=1 -Dorg.bouncycastle.jca.enable_jks=true -javaagent:/opt/socure/dd-java-agent.jar -Dnet.spy.log.LoggerImpl=net.spy.memcached.compat.log.SLF4JLogger"

deployment:
  replicaCount: 1
  podVolumes:
    - name: tmp
      emptyDir: {}
  podVolumeMounts:
    - name: tmp
      mountPath: /tmp
  nodeSelector: 
    #karpenter.sh/provisioner-name: default-amd64-ondemand
    kubernetes.io/os: linux
    karpenter.sh/provisioner-name: null
    karpenter.sh/nodepool: apps-default
  resources:
    requests:
      cpu: 100m

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 60
  # targetMemoryUtilizationPercentage: 60

istio:
  enabled: true
  hosts:
  - ai-vendor-service.webapps.us-east-1.product-dev.socure.link
  svcPort: 80
  private:
    gateway: private-gw
  public:
    enabled: false
  authorizationPolicy:
    enabled: true
    pa:
      enabled: true
      serviceAccounts:
        - "ai-gateway-service-dev"
