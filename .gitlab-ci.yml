stages:
  - validate
  - update-mscv
  - update-mscv-gov
  - build
  - wizscan
  - deploy
  - test
  - reg-tests

include:
  - project: 'plt/gitlab-pipeline-templates'
    file: socure-services.gitlab-ci.yml

variables:
  PROJECT_NAME: ai
  SERVICE_NAME: ai-vendor-service
  SERVICE_VERSION: 0.1.0
  DEV_ENVIRONMENT: "yes"
  MAVEN_EXTRA_ARGS: -Dbuild-environment=gitlab-ci
  MAVEN_FIPS_ARGS: -Dbuild-environment=gitlab-ci-fips
  MEND_PRODUCT_NAME: socure-saas
  JDK_VERSION: 17

reg-tests:
  stage: test
  image: node:21
  script:
    - cd /builds/product/ai/ai-vendor-service/regression-tests/ai_vendor_service
    - NODE_TLS_REJECT_UNAUTHORIZED=0 npm install -g @usebruno/cli
    - bru run --env Dev --insecure
    - test_result=$?
    - if [ $test_result -ne 0 ]; then
      echo "Tests failed with exit status $test_result";
      exit 1;
      else
      echo "All tests passed";
      exit 0;
      fi
  when: manual
  allow_failure: false