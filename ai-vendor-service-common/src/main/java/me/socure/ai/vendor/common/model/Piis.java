package me.socure.ai.vendor.common.model;

import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class Piis {
    @NonNull
    private final String firstName;
    @NonNull
    private final String surName;
    private final String middleName;
    private final String fullName;
    private final String company;
    private final String nationalId;
    private final String dob;
    private final String gender;
    @Builder.Default
    private final Boolean userConsent = false;
    private final String mobileNumber;
    private final String homeNumber;
    private final String streetNumber;
    private final String streetName;
    private final String streetAddress;
    private final String streetAddress2;
    private final String unitNumber;
    private final String city;
    private final String zipCode;
    private final String state;
    private final String email;
    private final String country;
    private final String entityName;
    private final String accountNumber;
    private final String routingNumber;
    private final String ein;
    private final String businessName;
}
