////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.common.model;

import java.util.Map;
import java.util.Set;

import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.jackson.Jacksonized;

/**
 * Ai vendor request class.
 */
@Data
@Builder
@Jacksonized
public class AiVendorRequest {

    @NonNull
    private final String transactionId;

    @NonNull
    private final Long accountId;

    private final Piis piis;
    private final Set<String> vendors;
    private final Map<String, Map<String, String>> vendorConfigs;
    private final Boolean maskPiiEnabled;

}
