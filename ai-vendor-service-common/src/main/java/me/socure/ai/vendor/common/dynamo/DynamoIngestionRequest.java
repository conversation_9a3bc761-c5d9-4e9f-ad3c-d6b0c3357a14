package me.socure.ai.vendor.common.dynamo;

import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class DynamoIngestionRequest {
    private String tableName;
    private String primaryKey;
    private String userName;
    private String sortKey;
    private Set<String> otherKeys;
    private String s3Bucket;
    private String s3BucketOwner;
    private String s3KeyPrefix;
    private String inputFormat;
    private String inputCompressionType;
    private String billingMode;
    private String kmsKey;
    private List<GSI> globalSecondaryIndexes;
}
