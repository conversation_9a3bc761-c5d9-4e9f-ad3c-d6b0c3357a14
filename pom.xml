<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ai-vendor-service</artifactId>
    <version>${revision}</version>
    <name>ai-vendor-service</name>
    <description>AI Vendor Service</description>

    <modules>
        <module>ai-vendor-service-common</module>
        <module>ai-vendor-service-core</module>
        <module>ai-vendor-service-dynamo</module>
        <module>ai-vendor-service-app</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <checkstyle.skip>true</checkstyle.skip>
        <java.version>17</java.version>
        <checkstyle.skip>true</checkstyle.skip>
        <socure.common.version>${revision}</socure.common.version>
    </properties>

    <parent>
        <groupId>me.socure</groupId>
        <artifactId>maven-root-java17</artifactId>
        <version>0.2-SNAPSHOT</version>
    </parent>

    <packaging>pom</packaging>
    <dependencies>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <!-- Import dependency management from Spring Boot -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>2.20.112</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-actor_2.11</artifactId>
                <version>2.5.16</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http2</artifactId>
                <version>4.1.108.Final</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M3</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
