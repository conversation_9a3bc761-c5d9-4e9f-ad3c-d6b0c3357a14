meta {
  name: trice_vendor_response
  type: http
  seq: 3
}

post {
  url: https://{{baseUrl}}/vendor/data
  body: json
  auth: none
}

body:json {
  {
    "transactionId": "123123123123123123",
    "accountId": "123",
    "maskPiiEnabled": false,
    "piis": {
      "firstName": "<PERSON>",
      "surName": "Kolb",
      "streetAddress": "546 old mill creek dr",
      "city": "woodway",
      "state": "TX",
      "zipCode": "76712",
      "country": "US",
      "nationalId": "*********",
      "dob": "********",
      "accountNumber": "*********",
      "routingNumber": "*********"
    },
    "vendors": [
      "trice"
    ],
    "vendorConfigs": {
      "trice": {
        "uri": "/hub/v1/parties",
        "http_method": "post",
        "template": "trice.body.create-party"
      }
    }
  }
}

assert {
  res.body.responses.length: eq 1
  res.body.responses[0].vendorName: eq trice
  res.body.responses[0].status: eq 201
  res.body.responses[0].response.name: eq <PERSON>
  res.body.responses[0].response.address.city: eq woodway
  res.body.responses[0].response.address.state: eq TX
  res.body.responses[0].response.bank_details.account_holder_name: eq Deborah Kolb
  res.body.responses[0].response.status: eq ok
}
