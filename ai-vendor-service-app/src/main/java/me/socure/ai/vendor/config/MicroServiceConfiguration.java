////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.config;

import me.socure.service.audit.client.TPAuditClient;
import me.socure.service.audit.client.TPAuditClient$;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.typesafe.config.Config;
import me.socure.common.config.MsConfigProvider$;
import org.springframework.context.annotation.Scope;

@Configuration
public class MicroServiceConfiguration {

    @Bean
    public Config config() {
        return MsConfigProvider$.MODULE$.provide().value();
    }

    @Bean
    @Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
    public TPAuditClient getThirdPartyAuditClient(final Config config) {
        return TPAuditClient$.MODULE$.apply(config);
    }
}
