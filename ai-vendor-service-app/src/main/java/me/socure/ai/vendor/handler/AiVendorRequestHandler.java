////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.handler;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Optional;
import java.util.stream.Stream;

import me.socure.ai.vendor.common.model.AiVendorRequest;
import me.socure.ai.vendor.core.model.VendorCallRequest;
import me.socure.ai.vendor.core.model.VendorCallResponse;
import me.socure.ai.vendor.model.VendorCallResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;

import me.socure.common.metrics.JavaMetricsFactory;
import me.socure.ai.vendor.core.handler.manager.VendorHandlersManager;
import me.socure.ai.vendor.core.handler.model.VendorHandler;
import me.socure.ai.vendor.core.utils.AiVendorServiceMetrics;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * Handles Ai vendor requests.
 */
@Component
public class AiVendorRequestHandler {

    private final VendorHandlersManager vendorHandlersManager;

    @Autowired
    public AiVendorRequestHandler(VendorHandlersManager vendorHandlersManager) {
        this.vendorHandlersManager = vendorHandlersManager;
    }

     public Mono<ServerResponse> handle(ServerRequest request) {
        return request
                .bodyToMono(AiVendorRequest.class)
                .flatMap(aiVendorRequest -> {
                    Stream<VendorCallRequest> vendorCallRequests = aiVendorRequest.getVendors().stream().parallel().map(vendorName ->
                            VendorCallRequest.builder()
                                    .transactionId(aiVendorRequest.getTransactionId())
                                    .accountId(aiVendorRequest.getAccountId())
                                    .vendorName(vendorName)
                                    .piis(aiVendorRequest.getPiis())
                                    .vendorConfig(Optional.ofNullable(aiVendorRequest.getVendorConfigs()).orElse(new HashMap<>()).get(vendorName))
                                    .maskPiiEnabled(aiVendorRequest.getMaskPiiEnabled())
                                    .build());
                    return Mono.just(vendorCallRequests);
                })
                .flatMap(vendorCallRequests -> Flux.fromStream(vendorCallRequests)
                        .parallel()
                        .runOn(Schedulers.boundedElastic())
                        .flatMap(req -> {
                            VendorHandler<?, ?> handler = vendorHandlersManager.getVendorHandler(req.getVendorName());
                            return handler.handle(req.getVendorName(), req);
                        })
                        .ordered(Comparator.comparingInt(VendorCallResponse::hashCode))
                        .collectList()
                        .flatMap(responses -> ServerResponse.ok().bodyValue(new VendorCallResponses(responses))));
    }

}
