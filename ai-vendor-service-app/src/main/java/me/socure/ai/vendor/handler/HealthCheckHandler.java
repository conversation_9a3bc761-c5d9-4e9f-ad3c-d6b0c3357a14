////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.handler;

import static org.springframework.web.reactive.function.server.ServerResponse.ok;

import me.socure.ai.vendor.response.APIResponse;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;

import reactor.core.publisher.Mono;

/**
 * Handler class for health check
 */

@Component
public class HealthCheckHandler {

    public Mono<ServerResponse> handleHealthCheckRequest(ServerRequest request) {
        return ok()
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new APIResponse<>(APIResponse.Status.OK, null));
    }

    public Mono<ServerResponse> handleZTDCheckRequest(ServerRequest request) {
        return ok()
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new APIResponse<>(APIResponse.Status.OK, System.getenv("DD_SERVICE")));
    }

}
