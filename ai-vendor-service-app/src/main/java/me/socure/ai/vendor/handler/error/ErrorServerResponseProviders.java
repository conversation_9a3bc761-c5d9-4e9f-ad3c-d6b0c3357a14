////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.handler.error;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.server.ResponseStatusException;

import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.response.APIResponse;
import reactor.core.publisher.Mono;

/**
 * Provides server responses for various error classes.
 */

@Log4j2
enum ErrorServerResponseProviders {


    RESPONSE_STATUS_EXCEPTION(ResponseStatusException.class) {
        @Override
        public Mono<ServerResponse> getResponse(Throwable ex) {
            return ServerResponse
                    .status(((ResponseStatusException) ex).getStatusCode())
                    .bodyValue(new APIResponse<>(APIResponse.Status.ERROR, Optional.ofNullable(ex.getMessage())));
        }
    },

    DEFAULT(Exception.class) {
        @Override
        public Mono<ServerResponse> getResponse(Throwable ex) {
            return ServerResponse
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .bodyValue(new APIResponse<>(APIResponse.Status.ERROR, Optional.ofNullable(ex.getMessage()).orElse("InternalServerException")));
        }
    };

    private final Class<?> clazz;

    private ErrorServerResponseProviders(Class<?> clazz) {
        this.clazz = clazz;
    }

    public Class<?> getClazz() {
        return clazz;
    }

    public abstract Mono<ServerResponse> getResponse(Throwable ex);

    private static final Map<Class<?>, ErrorServerResponseProviders> REVERSE_MAPPING = new ConcurrentHashMap<>();

    static {
        for (ErrorServerResponseProviders provider : ErrorServerResponseProviders.values()) {
            REVERSE_MAPPING.put(provider.getClazz(), provider);
        }
    }

    public static Mono<ServerResponse> getServerResponse(Throwable ex) {
        log.error("Error while processing the request", ex);
        return REVERSE_MAPPING.getOrDefault(ex.getClass(), DEFAULT).getResponse(ex);
    }

}
