////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.model;

import java.util.ArrayList;
import java.util.List;

import lombok.Builder;
import lombok.extern.jackson.Jacksonized;
import me.socure.ai.vendor.core.model.VendorCallResponse;

/**
 * All vendor call responses.
 */

@Jacksonized
@Builder
public class VendorCallResponses {

    private final List<VendorCallResponse<?>> vendorResponses = new ArrayList<>();

    public List<VendorCallResponse<?>> getResponses() {
        return vendorResponses;
    }

    public VendorCallResponses() {

    }

    public VendorCallResponses(List<VendorCallResponse<?>> responses) {
        responses.forEach(this::add);
    }

    public void add(VendorCallResponse<?> vendorCallResponse) {
        vendorResponses.add(vendorCallResponse);
    }

    public VendorCallResponses addAll(VendorCallResponses other) {
        vendorResponses.addAll(other.getResponses());
        return this;
    }

}
