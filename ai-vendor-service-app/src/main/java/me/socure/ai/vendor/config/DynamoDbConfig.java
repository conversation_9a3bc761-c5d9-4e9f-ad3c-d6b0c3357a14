package me.socure.ai.vendor.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.typesafe.config.Config;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient;

@Configuration
public class DynamoDbConfig {

    @Bean
    public DynamoDbAsyncClient getDynamoDbAsyncClient(Config config) {
        String region = config.getString("dynamo.region");
        int maxConcurrency = config.getInt("dynamo.max.concurrency");
        return DynamoDbAsyncClient.builder()
                .region(Region.of(region))
                .httpClientBuilder(NettyNioAsyncHttpClient.builder()
                        .tcpKeepAlive(true)
                        .useIdleConnectionReaper(false)
                        .maxConcurrency(maxConcurrency))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .build();
    }

}
