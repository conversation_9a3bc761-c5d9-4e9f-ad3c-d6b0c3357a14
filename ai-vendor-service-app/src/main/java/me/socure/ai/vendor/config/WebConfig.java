package me.socure.ai.vendor.config;

import static org.springframework.web.reactive.function.server.RequestPredicates.GET;
import static org.springframework.web.reactive.function.server.RequestPredicates.POST;

import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.handler.HealthCheckHandler;
import me.socure.ai.vendor.handler.AiVendorRequestHandler;
import org.springframework.boot.autoconfigure.web.WebProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.config.WebFluxConfigurer;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;

@Configuration
@Log4j2
class WebConfig implements WebFluxConfigurer {

    @Bean
    public RouterFunction<ServerResponse> getHealthRouterFunction(HealthCheckHandler healthCheckHandler) {
        return RouterFunctions
                .route(GET("/healthcheck"), healthCheckHandler::handleHealthCheckRequest)
                .andRoute(GET("/ztdcheck"), healthCheckHandler::handleZTDCheckRequest);
    }

    @Bean
    public RouterFunction<ServerResponse> getVendorDataRouterFunction(AiVendorRequestHandler aiVendorRequestHandler) {
        return RouterFunctions.route(POST("/vendor/data"), aiVendorRequestHandler::handle);
    }

    @Bean
    public WebProperties.Resources resources() {
        return new WebProperties.Resources();
    }

}
