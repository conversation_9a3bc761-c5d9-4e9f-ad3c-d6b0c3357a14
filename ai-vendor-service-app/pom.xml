<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>me.socure</groupId>
        <artifactId>ai-vendor-service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai-vendor-service-app</artifactId>

    <dependencies>
        
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>ai-vendor-service-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-metrics</artifactId>
            <version>${socure.common.version}</version>
        </dependency>


        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>ai-vendor-service-common</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>ai-vendor-service-dynamo</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.6.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>jib-fips</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci-fips</value>
                </property>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bc-fips</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bctls-fips</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcpkix-fips</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcpg-fips</artifactId>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <from>
                                <image>${env.JIB_JDK17_FIPS_IMAGE}</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>fips-registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SERVICE_NAME}</image>
                                <auth>
                                    <username>${env.REGISTRY_USER}</username>
                                    <password>${env.REGISTRY_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>latest-fips</tag>
                                    <tag>${env.FIPS_DOCKER_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.ai.vendor.AiVendorServiceApplication</mainClass>
                                <ports>
                                    <port>5000</port>
                                </ports>
                                <jvmFlags>
                                    <jvmFlag>--add-exports</jvmFlag>
                                    <jvmFlag>java.base/sun.security.util=ALL-UNNAMED</jvmFlag>
                                    <jvmFlag>--add-exports</jvmFlag>
                                    <jvmFlag>java.base/sun.security.provider=ALL-UNNAMED</jvmFlag>
                                </jvmFlags>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>jib</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <from>
                                <image>openjdk:17</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SERVICE_NAME}</image>
                                <auth>
                                    <username>${env.REGISTRY_USER}</username>
                                    <password>${env.REGISTRY_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>latest</tag>
                                    <tag>${env.NON_FIPS_DOCKER_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.ai.vendor.AiVendorServiceApplication</mainClass>
                                <ports>
                                    <port>5000</port>
                                </ports>
                                <jvmFlags>
                                    <jvmFlag>--add-exports</jvmFlag>
                                    <jvmFlag>java.base/sun.security.util=ALL-UNNAMED</jvmFlag>
                                    <jvmFlag>--add-exports</jvmFlag>
                                    <jvmFlag>java.base/sun.security.provider=ALL-UNNAMED</jvmFlag>
                                </jvmFlags>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
