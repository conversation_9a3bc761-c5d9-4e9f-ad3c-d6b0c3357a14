package me.socure.ai.vendor.core.builder.request.factory.impl;

import me.socure.ai.vendor.core.builder.request.model.TokenGenerateResponse;

import java.util.Base64;
import java.util.List;

import org.springframework.stereotype.Component;

import com.typesafe.config.Config;

@Component("cacheableBasicAuthKeyGenerator")
public class BasicAuthKeyGeneratorCustomFunctionImpl extends CacheableAuthKeyGeneratorCustomFunctionImpl {

	@Override
	public TokenGenerateResponse getAuthValue(List<String> inputElements, Config config) {
		String userName = inputElements.get(3);
		String password = inputElements.get(4);
		String token = "Basic " + Base64.getEncoder().encodeToString((userName + ":" + password).getBytes());
		return TokenGenerateResponse.builder().token(token).build();
	}
}