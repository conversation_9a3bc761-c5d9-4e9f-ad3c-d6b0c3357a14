package me.socure.ai.vendor.core.config;

import me.socure.common.timeout.NonBlockingFutureTimeout;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import scala.concurrent.ExecutionContext;
import scala.concurrent.JavaConversions;

import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledThreadPoolExecutor;

@Configuration
public class ExecutionContextConfig {

    @Value("${executioncontext.maxPoolSize}")
    private int maxPoolSize;

    @Value("${executioncontext.corePoolSize}")
    private int corePoolSize;

    @Value("${executioncontext.queueSize}")
    private int queueSize;

    @Value("${executioncontext.threadNamePrefix}")
    private  String threadNamePrefix;

    @Value("${nonblockingfuture.threads}")
    private int threadCount;

    private Executor getExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueSize);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.initialize();
        return executor;
    }

    @Bean
    public ExecutionContext getExecutionContext() {
        return JavaConversions.asExecutionContext(getExecutor());
    }

    @Bean
    public NonBlockingFutureTimeout getNonBlockingFuturetimeout() {
        return new NonBlockingFutureTimeout(new ScheduledThreadPoolExecutor(threadCount));
    }

}