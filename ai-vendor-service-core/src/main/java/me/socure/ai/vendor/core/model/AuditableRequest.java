////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2024 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.model;

/**
 * Interface for auditable request objects.
 */
public interface AuditableRequest {

    public Long getAccountId();

    public String getTransactionId();

    public Boolean getMaskPiiEnabled();

}
