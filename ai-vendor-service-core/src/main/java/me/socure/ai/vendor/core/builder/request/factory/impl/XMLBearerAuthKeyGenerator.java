package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import me.socure.ai.vendor.core.utils.XMLUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component("xmlCacheableBearerAuthKeyGenerator")
public class XMLBearerAuthKeyGenerator extends BearerAuthKeyGeneratorCustomFunctionImpl {

    private final ObjectMapper objectMapper;
    @Autowired
    public XMLBearerAuthKeyGenerator(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    protected JsonNode convertToJsonNode(ResponseEntity<String> entity) throws IOException {
        String jsonValue = XMLUtils.convertXMLtoJSON(entity.getBody());
        return objectMapper.readValue(jsonValue,JsonNode.class);
    }
}
