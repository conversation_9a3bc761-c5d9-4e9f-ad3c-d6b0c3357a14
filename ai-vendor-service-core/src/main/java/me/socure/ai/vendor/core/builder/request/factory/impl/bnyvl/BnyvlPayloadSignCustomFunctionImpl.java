package me.socure.ai.vendor.core.builder.request.factory.impl.bnyvl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import me.socure.ai.vendor.core.utils.SecretValueProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.List;
import java.util.Map;

@Component("bynvlPayloadSign")
public class BnyvlPayloadSignCustomFunctionImpl implements CustomFunction<String> {

    private static final Logger logger = LoggerFactory.getLogger(BnyvlPayloadSignCustomFunctionImpl.class);
    private static final String RSA_TYPE = "RSA";
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    private final SecretValueProvider secretValueProvider;

    private final ObjectMapper mapper;

    @Autowired
    public BnyvlPayloadSignCustomFunctionImpl(SecretValueProvider secretValueProvider, ObjectMapper mapper) {
        this.secretValueProvider = secretValueProvider;
        this.mapper = mapper;
    }


    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        if (inputElements.isEmpty())
            throw new RuntimeException("Input Elements Size is 0 for BnyvlPayloadSign Custom Function");
        else {
            String payload = inputElements.get(0);
            try {
                return signPayload(payload, config);
            } catch (Exception e) {
                throw new RuntimeException("Exception Occurred While Signing Payload for Bnyvl", e);
            }
        }
    }

    public String signPayload(String payload, Config config) {
        try {
            String bnySecretKeyPath = config.getString("configs.bnyvl.aws.secret.id");
            Map<String, String> secretMap = mapper.readValue(secretValueProvider.getSecretValue(bnySecretKeyPath), Map.class);
            String base64PrivateKey = secretMap.get("privateKey");
            byte[] privateKeyBytes = Base64.getDecoder().decode(base64PrivateKey);
            PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_TYPE);
            PrivateKey privateKey = keyFactory.generatePrivate(privateKeySpec);
            byte[] signatureBytes = sign(payload.getBytes(), privateKey, SIGNATURE_ALGORITHM);
            return Base64.getEncoder().encodeToString(signatureBytes);
        } catch (Exception ex) {
            logger.error("Error while preparing signature string for the request: ", ex);
            throw new RuntimeException("Error while signing the payload", ex);
        }
    }

    private static byte[] sign(byte[] payload, PrivateKey privateKey, String signAlgorithm) {
        try {
            Signature signature = Signature.getInstance(signAlgorithm);
            signature.initSign(privateKey);
            signature.update(payload);
            return signature.sign();
        } catch (Exception ex) {
            logger.error("Error while generating signature for the request: ", ex);
            throw new RuntimeException("Error while generating the signature", ex);
        }
    }
}
