package me.socure.ai.vendor.core.builder.request.factory.impl;

import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.typesafe.config.Config;

@Component("triceAddressGenerator")
@Log4j2
public class TriceGenerateAddressCustomFunctionImpl implements CustomFunction<String> {


	ObjectMapper objectMapper;

	@Autowired
	public TriceGenerateAddressCustomFunctionImpl(ObjectMapper objectMapper) {
		this.objectMapper = objectMapper;
	}

	//inputElements(0) should be input.streetAddress,
	//inputElements(1) should be input.streetAddress2,
	//inputElements(2) should be input.city,
	//inputElements(3) should be input.state,
	//inputElements(4) should be input.country,
	//inputElements(5) should be input.zip,
	//Output would be a json like below, if input.streetAddress and input.zip is not null. Else null would be returned.
	//{
	//    "city": "input.city",
	//    "country": "input.country",
	//    "line1": "input.streetAddress",
	//    "line2": "input.streetAddress2",
	//    "state": "input.state",
	//    "zip": "input.zipCode"
	//}

	@Override
	public Object executeCustomFunction(List<String> inputElements, Config config) {
		String streetAddress = inputElements.get(0);
		String streetAddress2 = inputElements.get(1);
		String city = inputElements.get(2);
		String state = inputElements.get(3);
		String country = inputElements.get(4);
		String zip = inputElements.get(5);

		try {
			if(StringUtils.isEmpty(streetAddress) || StringUtils.isEmpty(zip))
				return null;

			ObjectNode jsonNode = objectMapper.createObjectNode();

			jsonNode.put("line1", streetAddress);
			jsonNode.put("line2", streetAddress2);
			jsonNode.put("city", city);
			jsonNode.put("state", state);
			jsonNode.put("country", country);
			jsonNode.put("zip", zip);
			return jsonNode;
		}
		catch(Exception ex) {
			log.error("Exception occurred in triceAddressGenerator due to ", ex);
			return null;
		}
	}
}