package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component("mergeAddressResolver")
public class MergeAddressCustomFunctionImpl implements CustomFunction<String> {
    @Override
    public String executeCustomFunction(List<String> inputElements, Config config) {
        String physicalAddress = inputElements.get(0);
        String physicalAddress2 = inputElements.get(1);
        return Stream.of(physicalAddress, physicalAddress2)
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.joining(" "));
    }
}
