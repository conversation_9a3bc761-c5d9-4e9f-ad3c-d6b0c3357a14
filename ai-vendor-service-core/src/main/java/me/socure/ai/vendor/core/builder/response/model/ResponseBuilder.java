////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.builder.response.model;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.model.VendorCallResponse;
import reactor.core.publisher.Mono;

/**
 * Response builder interface.
 */
public interface ResponseBuilder<I> {

    public Mono<VendorCallResponse<?>> build(String vendorName, I response, Config defaultVendorConfig);

}
