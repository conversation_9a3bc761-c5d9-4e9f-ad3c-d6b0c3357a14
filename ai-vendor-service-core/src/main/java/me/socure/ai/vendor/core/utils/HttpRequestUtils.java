package me.socure.ai.vendor.core.utils;

import io.netty.channel.ChannelOption;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.timeout.ReadTimeoutException;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import io.netty.resolver.DefaultAddressResolverGroup;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.netty.http.client.HttpClient;

import java.io.ByteArrayInputStream;
import java.net.ConnectException;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Duration;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;

@Log4j2
@Component
public class HttpRequestUtils {

	private static final ObjectMapper MAPPER = new ObjectMapper();
	private  static SecretValueProvider secretValueProvider;

	@Autowired
	public HttpRequestUtils(SecretValueProvider secretValueProvider){
		HttpRequestUtils.secretValueProvider = secretValueProvider;
	}


	public static WebClient getWebClientWithSsl(Config connectionConfig, String endpoint, String uri) {
		boolean useNewHttpConnectionForEachRequest = connectionConfig.hasPath("useNewHttpConnectionForEachRequest") &&
				connectionConfig.getBoolean("useNewHttpConnectionForEachRequest");
		HttpClient httpClient = getHttpClientWithSsl(connectionConfig, useNewHttpConnectionForEachRequest);
		return WebClient
				.builder()
				.baseUrl(endpoint + uri)
				.clientConnector(new ReactorClientHttpConnector(httpClient))
				.build();
	}

	private static HttpClient getHttpClientWithSsl(Config connectionConfig, boolean useNewHttpConnectionForEachRequest) {
		HttpClient httpClient = getHttpClient(connectionConfig, useNewHttpConnectionForEachRequest);

		try {
			// Load SSL certificate and private key from config
			String bnySecretKeyPath = connectionConfig.getString("aws.secret.id");
			Map<String, String> secretMap = MAPPER.readValue(secretValueProvider.getSecretValue(bnySecretKeyPath), Map.class);
			String privateKey = secretMap.get("privateKey");
			String certificate = secretMap.get("certificate");

			// Parse the private key
			String privateKeyContent = privateKey
					.replace("-----BEGIN PRIVATE KEY-----", "")
					.replace("-----END PRIVATE KEY-----", "")
					.replaceAll("\\s+", "");
			byte[] keyBytes = Base64.getDecoder().decode(privateKeyContent);
			PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
			PrivateKey key = KeyFactory.getInstance("RSA").generatePrivate(keySpec);

			// Parse the certificate
			String certContent = certificate
					.replace("-----BEGIN CERTIFICATE-----", "")
					.replace("-----END CERTIFICATE-----", "")
					.replaceAll("\\s+", "");
			byte[] certBytes = Base64.getDecoder().decode(certContent);
			X509Certificate cert = (X509Certificate) CertificateFactory.getInstance("X.509")
					.generateCertificate(new ByteArrayInputStream(certBytes));

			// Create SSL context
			SslContext sslContext = SslContextBuilder.forClient()
					.keyManager(key, cert)
					.build();

			// Attach SSL context to HttpClient
			return httpClient.secure(ssl -> ssl.sslContext(sslContext));
		} catch (Exception e) {
			throw new RuntimeException("Failed to configure SSL for HttpClient", e);
		}
	}

	public static WebClient getWebClient(Config connectionConfig, String endpoint, String uri) {
		boolean useNewHttpConnectionForEachRequest = connectionConfig.hasPath("useNewHttpConnectionForEachRequest") && connectionConfig.getBoolean("useNewHttpConnectionForEachRequest");
		HttpClient httpClient = getHttpClient(connectionConfig, useNewHttpConnectionForEachRequest);
		return WebClient
			.builder()
			.baseUrl(endpoint + uri)
			.clientConnector(new ReactorClientHttpConnector(httpClient))
			.build();
	}

	public static HttpClient getHttpClient(Config config, boolean useNewHttpConnectionForEachRequest) {
		int connectTimeout = config.hasPath("connect.timeout") ? config.getInt("connect.timeout") : 5000;
		int writeTimeout = config.hasPath("write.timeout") ? config.getInt("write.timeout") : 60000;
		int readTimeout = config.hasPath("read.timeout") ? config.getInt("read.timeout") : 5000;
		int responseTimeout = config.hasPath("response.timeout") ? config.getInt("response.timeout") : 5000;
		if(useNewHttpConnectionForEachRequest)
			return HttpClient.newConnection()
				.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectTimeout)
				.responseTimeout(Duration.ofMillis(responseTimeout))
				.resolver(DefaultAddressResolverGroup.INSTANCE)
				.doOnConnected(
					conn -> {
						conn.addHandlerLast(new ReadTimeoutHandler(readTimeout, TimeUnit.MILLISECONDS));
						conn.addHandlerLast(new WriteTimeoutHandler(writeTimeout, TimeUnit.MILLISECONDS));
					}
				);
		else
			return HttpClient.create()
				.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectTimeout)
				.responseTimeout(Duration.ofMillis(responseTimeout))
				.resolver(DefaultAddressResolverGroup.INSTANCE)
				.doOnConnected(
					conn -> {
						conn.addHandlerLast(new ReadTimeoutHandler(readTimeout, TimeUnit.MILLISECONDS));
						conn.addHandlerLast(new WriteTimeoutHandler(writeTimeout, TimeUnit.MILLISECONDS));
					}
				);
	}

	public static Map<String, String> getAsMap(String headersStr) {
		Map<String, String> headersAsMap = new HashMap<>();
		try {
			headersAsMap = MAPPER.readValue(headersStr, new TypeReference<Map<String, String>>() {
			});
		}
		catch(JsonProcessingException e) {
			log.warn("Error in converting Header String to Map due to ", e);
		}
		return headersAsMap;
	}

	public static HttpHeaders getHttpHeaders(Map<String, String> headers) {
		HttpHeaders httpHeaders = new HttpHeaders();
		headers.forEach(httpHeaders::add);
		return httpHeaders;
	}

	public static Object getErrorMessage(String vendorName, Throwable t) {
		return getErrorMessage(vendorName, t, null, null);
	}

	public static String getErrorMessage(String vendorName, Throwable t, String txnID, Long accId) {
		String errorMessage;
		if(t instanceof WebClientResponseException.BadRequest) {
			try {
				errorMessage = MAPPER.readTree(((WebClientResponseException.BadRequest) t).getResponseBodyAsString()).toString();
			}
			catch(Exception e) {
				errorMessage = t.getMessage();
			}
		}
		else
			errorMessage = t.getMessage();
		log.error("Error occurred in calling the vendor: {}. TXN_ID: {}, ACC_ID: {}, Throwable Message: {} Parsed Error Message: {}", vendorName, txnID, accId, t.getMessage(), errorMessage);
		log.error("Error occurred in calling the vendor: {} TXN_ID: {} due to : ", vendorName, txnID, t);
		return errorMessage;
	}

	public static int computeHttpStatus(Throwable t) {
		if(t.getCause() instanceof ReadTimeoutException || t instanceof ReadTimeoutException || t instanceof TimeoutException)
			return HttpStatus.GATEWAY_TIMEOUT.value();
		else if(t instanceof ConnectException)
			return HttpStatus.SERVICE_UNAVAILABLE.value();
		else if(t instanceof HttpClientErrorException)
			return ((HttpClientErrorException) t).getStatusCode().value();
		else if(t instanceof HttpServerErrorException)
			return ((HttpServerErrorException) t).getStatusCode().value();
		else if(t instanceof WebClientResponseException)
			return ((WebClientResponseException) t).getStatusCode().value();
		else if(t instanceof IllegalArgumentException)
			return HttpStatus.BAD_REQUEST.value();
		else {
			log.info("Action Required!!! Unhandled error occurred during vendor call. Throwable:{}", t.getClass());
			return HttpStatus.INTERNAL_SERVER_ERROR.value();
		}
	}

	public static HttpMethod getHttpMethod(Map<String, String> requestConfigs) {
		if(Objects.nonNull(requestConfigs) && requestConfigs.containsKey("http_method")) {
			String method = requestConfigs.get("http_method").toLowerCase();
			return switch(method) {
				case "get" -> HttpMethod.GET;
				case "put" -> HttpMethod.PUT;
				case "delete" -> HttpMethod.DELETE;
				default -> HttpMethod.POST;
			};
		}
		return HttpMethod.POST;
	}
}