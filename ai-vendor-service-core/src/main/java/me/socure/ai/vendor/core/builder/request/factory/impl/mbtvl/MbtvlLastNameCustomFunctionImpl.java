package me.socure.ai.vendor.core.builder.request.factory.impl.mbtvl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("mbtvlLastName")
public class MbtvlLastNameCustomFunctionImpl implements CustomFunction<String> {
    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String businessName = inputElements.get(0);
        String lastName = inputElements.get(1);
        if (businessName == null || businessName.isEmpty()) {
            return lastName == null || lastName.isEmpty() ? "" : lastName;
        }
        return "";
    }
}

