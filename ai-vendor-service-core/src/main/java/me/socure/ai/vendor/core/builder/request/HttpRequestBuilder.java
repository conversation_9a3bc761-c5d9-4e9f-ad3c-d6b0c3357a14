////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.builder.request;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;

import me.socure.ai.vendor.core.builder.request.model.HttpRequest;
import me.socure.ai.vendor.core.builder.request.model.RequestBuilder;
import me.socure.ai.vendor.core.builder.request.utils.RequestTemplateHandler;
import me.socure.ai.vendor.core.model.VendorCallRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Mono;

/**
 * Request builder for HTTP vendors.
 */
@Log4j2
@Component
public class HttpRequestBuilder implements RequestBuilder<Mono<HttpRequest>> {

    private final ObjectMapper mapper;

    private final RequestTemplateHandler requestTemplateHandler;

    @Autowired
    public HttpRequestBuilder(ObjectMapper mapper, RequestTemplateHandler requestTemplateHandler) {
        this.mapper = mapper;
        this.requestTemplateHandler = requestTemplateHandler;
    }

    @Override
    public Mono<HttpRequest> build(String vendorName, VendorCallRequest vendorCallRequest, Config vendorConfig) {
        Supplier<HttpRequest> requestSupplier = () -> {
            String body = buildBody(vendorCallRequest, vendorConfig);
            Map<String, String> headers = buildHeaders(vendorCallRequest, vendorConfig);
            return HttpRequest.builder()
                .body(body)
                .headers(headers)
                .build();
        };
        return Mono.fromSupplier(requestSupplier);
    }

    private String buildBody(VendorCallRequest vendorCallRequest, Config vendorConfig) {
        String templateName = vendorConfig.hasPath("configs.default.template") ? vendorConfig.getString("configs.default.template") : "";
        if(Objects.nonNull(vendorCallRequest.getVendorConfig())) {
            templateName =
                vendorCallRequest
                    .getVendorConfig()
                    .getOrDefault("template", templateName);
        }
        if(StringUtils.isEmpty(templateName))
            throw new RuntimeException("Template name is required as default template is not available for the vendor");

        return requestTemplateHandler.buildRequest(templateName, vendorCallRequest, vendorConfig);
    }

    private Map<String, String> buildHeaders(VendorCallRequest vendorCallRequest, Config vendorConfig) {
        String headerTemplateName = vendorConfig.getString("configs.default.header.template");
        if(Objects.nonNull(vendorCallRequest.getVendorConfig())) {
            String headerTemplateNameFromRequest =
                vendorCallRequest
                    .getVendorConfig()
                    .getOrDefault("headerTemplate", headerTemplateName);
            headerTemplateName = StringUtils.isNotEmpty(headerTemplateNameFromRequest) ? headerTemplateNameFromRequest : headerTemplateName;
        }
        String requestHeadersAsStr = requestTemplateHandler.buildRequest(headerTemplateName, vendorCallRequest, vendorConfig);
        Map<String, String> headersAsMap = new HashMap<>();
        try {
            headersAsMap = mapper.readValue(requestHeadersAsStr, new TypeReference<>() {
            });
        }
        catch(JsonProcessingException e) {
            log.warn("Error in converting Header String to Map due to ", e);
        }
        return headersAsMap;
    }

    @Override
    public String getAsStr(Mono<HttpRequest> request) {
        return Objects.requireNonNull(request.block()).getBody(); // ideally request should already be generated, so block will not hold the thread for a very negligible time
    }
}