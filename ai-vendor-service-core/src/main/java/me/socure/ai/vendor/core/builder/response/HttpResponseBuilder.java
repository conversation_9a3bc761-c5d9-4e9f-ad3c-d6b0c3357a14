////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.builder.response;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.core.builder.response.model.ResponseBuilder;
import me.socure.ai.vendor.core.model.VendorCallResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.typesafe.config.Config;
import reactor.core.publisher.Mono;

import java.util.function.Supplier;

/**
 * Response builder for HTTP vendors.
 */
@Component
@Log4j2
public class HttpResponseBuilder implements ResponseBuilder<Mono<VendorCallResponse<?>>> {
    private final ObjectMapper objectMapper;

    @Autowired
    public HttpResponseBuilder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public Mono<VendorCallResponse<?>> build(String vendorName, Mono<VendorCallResponse<?>> responseMono, Config defaultVendorConfig) {
        return responseMono.flatMap(vendorCallResponse -> {
            Supplier<VendorCallResponse<?>> responseSupplier = () -> {
                VendorCallResponse<?> newResponse = null;
                try {
                    JsonNode jsonNode = objectMapper.readValue((String)vendorCallResponse.getResponse(), JsonNode.class);
                    newResponse = VendorCallResponse
                            .builder()
                            .response(jsonNode)
                            .vendorName(vendorCallResponse.getVendorName())
                            .status(vendorCallResponse.getStatus()).build();
                } catch (Exception e) {
                    log.error("Exception occurred while building http response with message "+e.getMessage());
                    return vendorCallResponse;
                }
                return newResponse;
            };
            return Mono.fromSupplier(responseSupplier);
        });
    }

}
