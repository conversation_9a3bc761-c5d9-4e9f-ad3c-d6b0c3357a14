package me.socure.ai.vendor.core.builder.request.factory.impl.bnyvl;

import com.typesafe.config.Config;
import me.socure.account.client.dashboard.AccountMetadataClient;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("bnyvlAddId")
public class BnyvlAddIdCustomFunction implements CustomFunction<String> {

    @Autowired
    private AccountMetadataClient accountMetadataClient;

    private static final Map<String, String> ACCOUNT_ID_MAPPING = Map.of(
            //"", "**********",  // TMRS?
            //"", "**********",  // Public?
            "2490", "**********"  // American First Finance
    );

    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String accountId = inputElements.get(0);
        String addId = "";
        if (accountId != null) {
            addId = ACCOUNT_ID_MAPPING.get(accountId);
        }
        return addId;
    }
}
