package me.socure.ai.vendor.core.builder.request.factory.impl.mbtvl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("mbtvlTinType")
public class MbtvlTintypeCustomFunctionImpl implements CustomFunction<String> {
    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String businessName = inputElements.get(0);
        return businessName != null && !businessName.isEmpty() ? "3" : "1";
    }
}
