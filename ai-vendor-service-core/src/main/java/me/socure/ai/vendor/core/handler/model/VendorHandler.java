////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.handler.model;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.core.builder.request.model.RequestBuilder;
import me.socure.ai.vendor.core.builder.response.model.ResponseBuilder;
import me.socure.ai.vendor.core.handler.TPAuditHandler;
import me.socure.ai.vendor.core.model.Auditable;
import me.socure.ai.vendor.core.model.VendorCallRequest;
import me.socure.ai.vendor.core.model.VendorCallResponse;
import me.socure.ai.vendor.core.utils.AiVendorServiceMetrics;
import me.socure.ai.vendor.core.utils.ValueHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import com.typesafe.config.Config;

import lombok.Getter;
import me.socure.common.metrics.JavaMetricsFactory;
import reactor.core.publisher.Mono;

/**
 * Base vendor handler.
 */
@Log4j2
public abstract class VendorHandler<VREQ, VRES> implements Auditable {

    @Getter
    private final RequestBuilder<VREQ> requestBuilder;

    private final ResponseBuilder<VRES> responseBuilder;

    protected final Config vendorConfig;

    private final AiVendorServiceMetrics aiVendorServiceMetrics;

    private final TPAuditHandler tpAuditHandler;

    protected VendorHandler(RequestBuilder<VREQ> requestBuilder, ResponseBuilder<VRES> responseBuilder, Config vendorConfig, TPAuditHandler tpAuditHandler) {
        this.requestBuilder = requestBuilder;
		this.responseBuilder = responseBuilder;
		this.vendorConfig = vendorConfig;
        this.tpAuditHandler = tpAuditHandler;
        this.aiVendorServiceMetrics = new AiVendorServiceMetrics(JavaMetricsFactory.get("vendor.handler"));
    }

    public Mono<VendorCallResponse<?>> handle(String vendorName, VendorCallRequest request) {
        String vendorTag = "vendor_name:" + vendorName;
        Map<String, String> requestConfigs = request.getVendorConfig();
        String uriFromRequestConfig = Objects.nonNull(requestConfigs) && StringUtils.isNotEmpty(requestConfigs.get("uri")) ? requestConfigs.get("uri") : "";
        Date startTime = new Date();
        ValueHolder<VREQ> requestValueHolder = new ValueHolder<>();
        log.info("received handle request for vendor: {}", vendorName);
        try {
            VREQ vendorRequest = requestValueHolder
                    .setAndGetValue(() -> requestBuilder.build(vendorName, request, vendorConfig));

            VRES vendorResponse = processRequest(vendorName, vendorRequest, request.getVendorConfig(), request.getTransactionId(), request.getAccountId());
            return responseBuilder.build(vendorName, vendorResponse, vendorConfig.getConfig("configs"))
                    .map(response -> {
                        long responseTime = System.currentTimeMillis() - startTime.getTime();
                        String httpStatusTag = "http_status:"+ response.getStatus();
                        aiVendorServiceMetrics.time("external.call.time", responseTime, vendorTag, httpStatusTag);
                        auditAsync(request, response, uriFromRequestConfig, requestBuilder.getAsStr(vendorRequest), startTime);
                        return response;
                    });
        } catch (Throwable t) {
            log.error("exception occured while calling vendor: {} exception: ",vendorName, t);
            return Mono.just(VendorCallResponse.builder()
                            .status(HttpStatus.INTERNAL_SERVER_ERROR.value())
                            .vendorName(vendorName)
                            .response(t.getMessage())
                            .build())
                    .map(response -> {
                        long responseTime = System.currentTimeMillis() - startTime.getTime();
                        String httpStatusTag = "http_status:"+ response.getStatus();
                        aiVendorServiceMetrics.time("external.call.time", responseTime, vendorTag, httpStatusTag);
                        auditAsync(request, response, uriFromRequestConfig, requestBuilder.getAsStr(requestValueHolder.getValue()), startTime);
                        return response;
                    });
        }
    }

    protected abstract VRES processRequest(
            String vendorName,
            VREQ request,
            Map<String, String> requestConfigs,
            String transactionId,
            Long accountId
    );

    @Override
    public TPAuditHandler getTPAuditHandler() {
        return tpAuditHandler;
    }

}
