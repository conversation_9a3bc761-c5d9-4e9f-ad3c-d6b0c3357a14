////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.model;

import java.util.Map;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;
import me.socure.ai.vendor.common.model.Piis;

/**
 * Request details for making vendor calls.
 */

@Data
@Jacksonized
@Builder
public class VendorCallRequest implements AuditableRequest {

    private final String transactionId;
    private final Long accountId;
    private final String vendorName;
    private final Piis piis;
    private final Map<String, String> vendorConfig;
    private final Boolean maskPiiEnabled;

    public Boolean getMaskPiiEnabled() {
        return maskPiiEnabled != null && maskPiiEnabled;
    }

    public static VendorCallRequest empty(String vendorName) {
        return VendorCallRequest.builder().vendorName(vendorName).build();
    }
}
