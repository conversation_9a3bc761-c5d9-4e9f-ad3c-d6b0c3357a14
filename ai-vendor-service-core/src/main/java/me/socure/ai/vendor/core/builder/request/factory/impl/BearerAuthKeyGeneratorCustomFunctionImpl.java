package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.typesafe.config.Config;

import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.core.builder.request.model.TokenGenerateResponse;
import me.socure.ai.vendor.core.utils.HttpRequestUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import reactor.core.publisher.Mono;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Log4j2
public abstract class BearerAuthKeyGeneratorCustomFunctionImpl extends CacheableAuthKeyGeneratorCustomFunctionImpl {

	@Override
	public TokenGenerateResponse getAuthValue(List<String> inputElements, Config config) {
		String authorizationRequestBody = inputElements.get(3);
		String authorizationRequestHeader = inputElements.get(4);
		Map<String, String> headersAsMap = HttpRequestUtils.getAsMap(authorizationRequestHeader);
		String contentType = headersAsMap.get("Content-Type");
		String tokenPath = inputElements.get(5);
		String expiresInPath = inputElements.size() >= 7 ? inputElements.get(6) : null;
		String vendorName = config.getString("name");

		Config connectionConfig = config.getConfig("connection");
		String endpoint = connectionConfig.getString("authorization.endpoint");
		String authorizationUri = connectionConfig.getString("authorization.uri");
        boolean sslBased = connectionConfig.getBoolean("ssl.enabled");

		WebClient webClient = null;
		if(sslBased) webClient = HttpRequestUtils.getWebClientWithSsl(connectionConfig, endpoint, authorizationUri);
		else webClient = HttpRequestUtils.getWebClient(connectionConfig, endpoint, authorizationUri);

		Mono<ResponseEntity<String>> responseEntityMono;
		if("application/x-www-form-urlencoded".equalsIgnoreCase(contentType)) {
			Map<String, String> bodyAsMap = HttpRequestUtils.getAsMap(authorizationRequestBody);

			MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
			bodyAsMap.forEach(formData::add);  // Convert map to MultiValueMap

			// Handle form data
			responseEntityMono = webClient
				.post()
				.headers(httpHeaders -> httpHeaders.addAll(HttpRequestUtils.getHttpHeaders(headersAsMap)))
				.contentType(MediaType.APPLICATION_FORM_URLENCODED)
				.body(BodyInserters.fromFormData(formData)) // Use the MultiValueMap for form data
				.retrieve()
				.toEntity(String.class);
		}
		else {
			// Handle JSON or other content types
			responseEntityMono = webClient
				.post()
				.headers(httpHeaders -> httpHeaders.addAll(HttpRequestUtils.getHttpHeaders(headersAsMap)))
				.bodyValue(authorizationRequestBody) // Assuming JSON by default
				.retrieve()
				.toEntity(String.class);
		}

		return responseEntityMono
			.map(entity -> {
				try {
					JsonNode jsonNode = convertToJsonNode(entity);
					String token = jsonNode.at(tokenPath).asText();
					if(StringUtils.isNotEmpty(token) && !token.contains("Bearer")) {
						token = "Bearer " + token;
					}
					Long expiresIn = StringUtils.isEmpty(expiresInPath) ? null : jsonNode.at(expiresInPath).asLong();
					log.info("Token generated for vendor: {}", vendorName);
					return TokenGenerateResponse.builder().token(token).expiresIn(expiresIn).build();
				}
				catch(IOException e) {
					throw new RuntimeException("IOException occurred when processing response", e);
				}
			})
			.onErrorResume(Throwable.class, t -> {
				HttpRequestUtils.getErrorMessage(vendorName, t);
				return Mono.just(TokenGenerateResponse.builder().token("").build());
			})
			.block();
	}

	protected abstract JsonNode convertToJsonNode(ResponseEntity<String> entity) throws IOException;
}