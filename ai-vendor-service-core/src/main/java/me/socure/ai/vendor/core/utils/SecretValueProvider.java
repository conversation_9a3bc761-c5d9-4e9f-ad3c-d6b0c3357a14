package me.socure.ai.vendor.core.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;

@Component
public class SecretValueProvider {
    private final SecretsManagerClient client;

    @Autowired
    public SecretValueProvider(SecretsManagerClient client) {
        this.client = client;
    }

    public String getSecretValue(String secretName) {
        // TODO(PRANSHU): cache keys
        GetSecretValueRequest getSecretValueRequest = GetSecretValueRequest.builder().secretId(secretName).build();
        return client.getSecretValue(getSecretValueRequest).secretString();
    }
}
