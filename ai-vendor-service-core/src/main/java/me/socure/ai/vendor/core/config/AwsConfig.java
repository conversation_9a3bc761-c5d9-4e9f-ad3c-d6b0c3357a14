package me.socure.ai.vendor.core.config;

import com.typesafe.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.kms.KmsClient;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;

@Configuration
public class AwsConfig {

    @Autowired
    Config config;
    @Bean
    public SecretsManagerClient secretsManagerClient() {
        return SecretsManagerClient.builder().build();
    }

    @Bean
    public KmsClient kmsClient() {
        String region = config.getString("socure.kms.region");
        return KmsClient.builder().region(Region.of(region)).build();
    }
}
