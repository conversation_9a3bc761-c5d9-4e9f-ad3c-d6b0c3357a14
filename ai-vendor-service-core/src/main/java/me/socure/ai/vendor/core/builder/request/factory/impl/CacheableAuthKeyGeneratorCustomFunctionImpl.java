package me.socure.ai.vendor.core.builder.request.factory.impl;

import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import me.socure.ai.vendor.core.builder.request.model.CacheKey;
import me.socure.ai.vendor.core.builder.request.model.TokenGenerateResponse;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.google.common.annotations.VisibleForTesting;
import com.typesafe.config.Config;

@Log4j2
@EnableScheduling
public abstract class CacheableAuthKeyGeneratorCustomFunctionImpl implements CustomFunction<String> {

	private final Map<CacheKey, String> authKeysCache = new ConcurrentHashMap<>();

	@VisibleForTesting
	Map<CacheKey, String> getAuthKeysCache() {
		return authKeysCache;
	}

	@Override
	public Object executeCustomFunction(List<String> inputElements, Config config) {
		String cacheKeyName = inputElements.get(1);

		CacheKey searchKey = CacheKey.builder().cacheKeyName(cacheKeyName).build();
		String authValueFromCache = authKeysCache.get(searchKey);
		if(authKeysCache.containsKey(searchKey))
			return authValueFromCache;
		else {
			TokenGenerateResponse tokenGenerateResponse = getAuthValue(inputElements, config);
			updateCache(null, cacheKeyName, tokenGenerateResponse, inputElements, config);
			return tokenGenerateResponse.getToken();
		}
	}

	public abstract TokenGenerateResponse getAuthValue(List<String> inputElements, Config config);

	//This method is scheduled to be invoked every 30minutes (1800000 milliseconds)
	@Scheduled(fixedRate = 1800000)
	public void refreshKeys() {
		for(Map.Entry<CacheKey, String> entry : authKeysCache.entrySet()) {
			CacheKey cacheKey = entry.getKey();
			long currentTime = System.currentTimeMillis();
			if(cacheKey.getRefreshTime() != null && cacheKey.getRefreshTime() < currentTime) {
				log.info("Refreshing the cacheKey {} since the refreshTime: {} is behind currentTime: {}", cacheKey.getCacheKeyName(), cacheKey.getRefreshTime(), currentTime);
				List<String> inputElements = cacheKey.getMetaData();
				String cacheKeyName = inputElements.get(1);
				TokenGenerateResponse tokenGenerateResponse = getAuthValue(inputElements, cacheKey.getConfig());
				updateCache(cacheKey, cacheKeyName, tokenGenerateResponse, inputElements, cacheKey.getConfig());
			}
		}
	}

	private void updateCache(CacheKey existingCacheKey, String cacheKeyName, TokenGenerateResponse tokenGenerateResponse, List<String> inputElements, Config config) {
		String cacheValue = tokenGenerateResponse.getToken();
		if(StringUtils.isNotEmpty(cacheValue)) {
			Long currentTime = System.currentTimeMillis();
			Long refreshTime = getRefreshTime(inputElements, currentTime, tokenGenerateResponse);

			CacheKey cacheKeyToPersist =
				existingCacheKey != null ? existingCacheKey.withUpdatedTimestamps(currentTime, refreshTime) :
					CacheKey.builder()
						.cacheKeyName(cacheKeyName)
						.metaData(inputElements)
						.config(config)
						.createdTime(currentTime)
						.refreshTime(refreshTime)
						.build();

			//Remove the existing cacheKey if any, and persist the new cacheKey
			if(existingCacheKey != null)
				authKeysCache.remove(existingCacheKey);
			authKeysCache.put(cacheKeyToPersist, cacheValue);
		}
	}

	private Long getRefreshTime(List<String> inputElements, Long currentTime, TokenGenerateResponse tokenGenerateResponse) {
		long ttl = Long.parseLong(inputElements.get(2));
		if(ttl != -1L)
			return currentTime + ttl;

		String expiresInType = inputElements.size() >= 8 ? inputElements.get(7) : "";
		Long expiresIn = tokenGenerateResponse.getExpiresIn();
		Long expiresInMillis = getTimeInMillis(expiresInType, expiresIn);

		Long refreshTime = null;

		if(expiresInMillis != null) {
			expiresInMillis = expiresInMillis < TimeUnit.HOURS.toMillis(1) ? TimeUnit.MINUTES.toMillis(10) : expiresInMillis - TimeUnit.HOURS.toMillis(1);
			refreshTime = currentTime + expiresInMillis;
		}
		return refreshTime;
	}

	private Long getTimeInMillis(String expiresInType, Long expiresIn) {
		if(expiresIn == null)
			return null;
		else if("seconds".equals(expiresInType))
			return expiresIn * 1000;
		else
			return expiresIn;
	}
}