////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.handler.manager;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import me.socure.ai.vendor.core.builder.request.manager.RequestBuildersManager;
import me.socure.ai.vendor.core.builder.response.manager.ResponseBuildersManager;
import me.socure.ai.vendor.core.handler.HTTPVendorHandler;
import me.socure.ai.vendor.core.handler.TPAuditHandler;
import me.socure.ai.vendor.core.handler.UnknownVendorHandler;
import me.socure.ai.vendor.core.utils.HttpRequestUtils;
import me.socure.service.audit.client.TPAuditClient;
import me.socure.thirdparty.audit.common.ThirdPartyServiceIds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.handler.model.VendorHandler;

/**
 * Manages the vendor level handlers.
 */
@Component
public class VendorHandlersManager {

    private final RequestBuildersManager requestBuildersManager;
    private final ResponseBuildersManager responseBuildersManager;
    private final UnknownVendorHandler unknownVendorHandler;
    private final Map<String, VendorHandler<?, ?>> vendorHandlersMap = new ConcurrentHashMap<>();
    private final TPAuditClient tpAuditClient;

    @Autowired
    public VendorHandlersManager(Config config, RequestBuildersManager requestBuildersManager,
                                 ResponseBuildersManager responseBuildersManager, UnknownVendorHandler unknownVendorHandler,
                                 TPAuditClient tpAuditClient) {
        this.requestBuildersManager = requestBuildersManager;
		this.responseBuildersManager = responseBuildersManager;
        this.unknownVendorHandler = unknownVendorHandler;
        this.tpAuditClient = tpAuditClient;
		List<? extends Config> vendorConfigList = config.getConfigList("vendor.definitions");
        for (Config vendorConfig : vendorConfigList) {
            String vendorName = vendorConfig.getString("name");
            String vendorType = vendorConfig.getString("type");
            vendorHandlersMap.put(vendorName, initVendor(vendorType, vendorConfig));
        }
    }

    public VendorHandler<?, ?> getVendorHandler(String vendorName) {
        return vendorHandlersMap.getOrDefault(vendorName, unknownVendorHandler);
    }

    private VendorHandler<?, ?> initVendor(String vendorType, Config config) {
        switch (vendorType) {
            case "http":
                Config connectionConfig = config.getConfig("connection");
                String endpoint = connectionConfig.getString("endpoint");
                String uri = connectionConfig.getString("uri");

                Config builderConfig = config.getConfig("builders");
                TPAuditHandler tpAuditHandler = null;
                if (config.hasPath("tp.audit.service.id")) {
                    int serviceId = config.getInt("tp.audit.service.id");
                    tpAuditHandler = new TPAuditHandler(tpAuditClient, ThirdPartyServiceIds.getByServiceId(serviceId));
                }
                return new HTTPVendorHandler(
                    HttpRequestUtils.getWebClient(connectionConfig, endpoint, uri),
                    requestBuildersManager.getRequestBuilder(builderConfig.getString("request")),
                    responseBuildersManager.getResponseBuilder(builderConfig.getString("response")),
                    config,
                    tpAuditHandler
                );
            default:
                throw new RuntimeException("Unsupported vendor type " + vendorType);
        }
    }
}
