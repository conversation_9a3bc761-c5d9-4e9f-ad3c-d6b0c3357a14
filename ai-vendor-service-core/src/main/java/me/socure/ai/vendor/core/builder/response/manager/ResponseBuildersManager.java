////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.builder.response.manager;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import me.socure.ai.vendor.core.builder.response.XMLResponseBuilder;
import me.socure.ai.vendor.core.builder.response.model.ResponseBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import me.socure.ai.vendor.core.builder.response.HttpResponseBuilder;

/**
 * Manages request builders.
 */

@Component
public class ResponseBuildersManager {

    private final Map<String, ResponseBuilder> responseBuildersMap = new ConcurrentHashMap<>();

    @Autowired
    public ResponseBuildersManager(HttpResponseBuilder httpResponseBuilder, XMLResponseBuilder xmlResponseBuilder) {
        responseBuildersMap.put("http_response_builder", httpResponseBuilder);
        responseBuildersMap.put("xml_response_builder", xmlResponseBuilder);
    }

    public ResponseBuilder getResponseBuilder(String name) {
        return responseBuildersMap.get(name);
    }

}
