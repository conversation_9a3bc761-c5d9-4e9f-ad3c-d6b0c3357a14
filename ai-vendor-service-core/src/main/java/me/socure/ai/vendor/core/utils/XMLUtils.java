package me.socure.ai.vendor.core.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.ser.ToXmlGenerator;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class XMLUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static  String convertXMLtoJSON(String xmlPayload, List<String> fieldsToAlwaysBeArrays) throws IOException {
        XmlMapper xmlMapper = new XmlMapper();
        xmlMapper.configure(ToXmlGenerator.Feature.WRITE_XML_DECLARATION, true);
        JsonNode jsonNode = xmlMapper.readTree(xmlPayload.getBytes());
        ensureFieldsAreArrays(jsonNode, fieldsToAlwaysBeArrays);
        String value = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonNode);
        return value;
    }

    public static  String convertXMLtoJSON(String xmlPayload) throws IOException {
       return convertXMLtoJSON(xmlPayload, new ArrayList<>());
    }


    /**
     * Ensures that specified fields in a JSON node are always treated as arrays.
     *
     * In an XML response converted to JSON, a node with one element is converted to an object,
     * while a node with multiple elements is converted to an array. This method ensures that
     * the fields listed in fieldsToAlwaysBeArrays are always treated as arrays of objects,
     * regardless of the number of elements.
     *
     * For example:
     * <creditCard sourcedFrom="INR">
     *     <dob>1983-05-12</dob>
     * </creditCard>
     * This will be converted to:
     * "creditCard": {
     *     "dob": "1983-05-12"
     * }
     *
     * And:
     * <creditCard sourcedFrom="INR">
     *     <dob>1983-05-12</dob>
     * </creditCard>
     * <creditCard sourcedFrom="INR">
     *     <dob>1982-05-12</dob>
     * </creditCard>
     * This will be converted to:
     * "creditCard": [
     *     {
     *         "dob": "1983-05-12"
     *     },
     *     {
     *         "dob": "1982-05-12"
     *     }
     * ]
     *
     * This method modifies the JSON node to ensure consistent array handling for the specified fields.
     *
     * @param node the JSON node to process
     * @param fieldsToAlwaysBeArrays the list of field names that should always be treated as arrays
     */
    private static void ensureFieldsAreArrays(JsonNode node, List<String> fieldsToAlwaysBeArrays) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            fieldsToAlwaysBeArrays.forEach(field -> {
                if (objectNode.has(field)) {
                    JsonNode fieldNode = objectNode.get(field);
                    if (!fieldNode.isArray()) {
                        ArrayNode arrayNode = objectMapper.createArrayNode();
                        arrayNode.add(fieldNode);
                        objectNode.set(field, arrayNode);
                    }
                }
            });
            objectNode.fields().forEachRemaining(entry -> ensureFieldsAreArrays(entry.getValue(), fieldsToAlwaysBeArrays));
        } else if (node.isArray()) {
            for (JsonNode item : node) {
                ensureFieldsAreArrays(item, fieldsToAlwaysBeArrays);
            }
        }
    }

}
