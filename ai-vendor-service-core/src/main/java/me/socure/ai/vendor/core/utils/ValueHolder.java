////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2024 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.utils;

import java.util.function.Supplier;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Holds value of any type.
 */
@NoArgsConstructor
@Getter
@Setter
public class ValueHolder<T> {

    private T value = null;

    public T setAndGetValue(Supplier<T> supplier) {
        this.value = supplier.get();
        return this.value;
    }

}
