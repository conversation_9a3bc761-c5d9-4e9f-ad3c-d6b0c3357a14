package me.socure.ai.vendor.core.builder.response;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.typesafe.config.Config;
import me.socure.ai.vendor.core.utils.XMLUtils;
import me.socure.ai.vendor.core.builder.response.model.ResponseBuilder;
import me.socure.ai.vendor.core.model.VendorCallResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.util.List;
import java.util.function.Supplier;

@Component
public class XMLResponseBuilder implements ResponseBuilder<Mono<VendorCallResponse<?>>> {

    private final ObjectMapper objectMapper;

    @Autowired
    public XMLResponseBuilder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public Mono<VendorCallResponse<?>> build(String vendorName, Mono<VendorCallResponse<?>> responseMono, Config defaultVendorConfig) {
        return responseMono.flatMap(vendorCallResponse -> {
            Supplier<VendorCallResponse<?>> responseSupplier = () -> {
                VendorCallResponse<?> newResponse = null;
                try {
                    String responseAsJSON;
                    if (defaultVendorConfig.hasPath("always_array_list")) {
                        List<String> alwaysArrayList = defaultVendorConfig.getStringList("always_array_list");
                        responseAsJSON = XMLUtils.convertXMLtoJSON(vendorCallResponse.getResponseStr(), alwaysArrayList);
                    } else {
                        responseAsJSON = XMLUtils.convertXMLtoJSON(vendorCallResponse.getResponseStr());
                    }
                    ObjectNode objectNode = objectMapper.readValue(responseAsJSON, ObjectNode.class);
                    newResponse = VendorCallResponse
                            .builder()
                            .response(objectNode)
                            .vendorName(vendorCallResponse.getVendorName())
                            .status(vendorCallResponse.getStatus()).build();
                } catch (IOException e) {
                    return vendorCallResponse;
                }
                return newResponse;
            };
            return Mono.fromSupplier(responseSupplier);
        });
    }
}
