package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("fullNameResolver")
public class FullNameCustomFunctionImpl implements CustomFunction<String> {
	@Override public String executeCustomFunction(List<String> inputElements, Config config) {
		String fullName = inputElements.get(0);
		String firstName = inputElements.get(1);
		String middleName = inputElements.get(2);
		String surName = inputElements.get(3);
		if(StringUtils.isNotBlank(fullName))
			return fullName;
		else {
			if(StringUtils.isNotBlank(middleName))
				return firstName.concat(" ").concat(middleName).concat(" ").concat(surName);
			else
				return firstName.concat(" ").concat(surName);
		}
	}
}
