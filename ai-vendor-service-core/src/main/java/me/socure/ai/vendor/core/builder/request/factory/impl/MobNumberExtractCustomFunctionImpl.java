package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("mobNumberExtract")
public class MobNumberExtractCustomFunctionImpl implements CustomFunction<String> {
    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String input = inputElements.get(0);
        String cleanInput = input != null ? input.replaceAll("\\D", "") : "";
        return cleanInput.length() > 10 ? cleanInput.substring(cleanInput.length() - 10) : cleanInput;
    }
}
