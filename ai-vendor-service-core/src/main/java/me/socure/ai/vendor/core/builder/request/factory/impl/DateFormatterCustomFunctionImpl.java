package me.socure.ai.vendor.core.builder.request.factory.impl;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import com.typesafe.config.Config;

@Component("dateFormatter")
public class DateFormatterCustomFunctionImpl implements CustomFunction<String> {

    // First Element Should be Date String, Second Element Current Date Format, Third Element Expected Date Format
    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String dateString = inputElements.get(0);
        String currentFormat = inputElements.get(1);
        String expectedFormat = inputElements.get(2);
        try {
            DateFormat dateFormatter = new SimpleDateFormat(currentFormat);
            Date date = dateFormatter.parse(dateString);
            SimpleDateFormat formatter = new SimpleDateFormat(expectedFormat);
            String formattedDate = formatter.format(date);
            return formattedDate;
        } catch (ParseException ex) {
            throw new RuntimeException("ParseException occurred in DateFormatterCustomFunctionImpl for date" + dateString + " format " + currentFormat, ex);
        }
    }
}
