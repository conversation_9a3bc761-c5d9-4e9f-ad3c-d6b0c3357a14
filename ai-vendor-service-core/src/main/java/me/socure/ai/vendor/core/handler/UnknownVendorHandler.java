////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.handler;

import java.util.Map;

import me.socure.ai.vendor.core.builder.request.model.RequestBuilder;
import me.socure.ai.vendor.core.builder.response.model.ResponseBuilder;
import me.socure.ai.vendor.core.model.VendorCallRequest;
import me.socure.ai.vendor.core.model.VendorCallResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.handler.model.VendorHandler;
import reactor.core.publisher.Mono;

/**
 * Handler for unknown vendors.
 */
@Component
public class UnknownVendorHandler extends VendorHandler<String, Mono<VendorCallResponse<?>>> {

    private static final RequestBuilder<String> REQUEST_BUILDER = new RequestBuilder<>() {
        @Override
        public String build(String vendorName, VendorCallRequest request, Config defaultVendorConfig) {
            return null;
        }

        @Override
        public String getAsStr(String request) {
            return request;
        }
    };

    private static final ResponseBuilder<Mono<VendorCallResponse<?>>> RESPONSE_BUILDER = (vendorName, response, defaultVendorConfig) -> response;

    @Autowired
    public UnknownVendorHandler() {
        super(REQUEST_BUILDER, RESPONSE_BUILDER, null, null);
    }

    @Override
    protected Mono<VendorCallResponse<?>> processRequest(
            String vendorName,
            String request,
            Map<String, String> requestConfigs,
            final String transactionId,
            final Long accountId
    ) {
        return Mono.just(VendorCallResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .vendorName(vendorName)
                .response("Unknown vendor name: " + vendorName)
                .build());
    }

    @Override
    public String getRequestUrl(String dynamicUri) {
        return "";
    }
}
