////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.builder.request.model;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.model.VendorCallRequest;

/**
 * Request builder interface.
 */
public interface RequestBuilder<O> {

    O build(String vendorName, VendorCallRequest request, Config vendorConfig);

    String getAsStr(O request);

}