////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2024 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.model;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import me.socure.ai.vendor.core.handler.TPAuditHandler;

/**
 * Interface for auditable objects.
 */
public interface Auditable {

    TPAuditHandler getTPAuditHandler();

    String getRequestUrl(String dynamicUri);

    default void audit(AuditableRequest request, AuditableResponse response, String dynamicUri, String requestBody, Date startTime) {
        TPAuditHandler tpAuditHandler = getTPAuditHandler();
        if (Objects.nonNull(tpAuditHandler)) {
            tpAuditHandler.audit(
                    request.getAccountId(),
                    request.getTransactionId(),
                    startTime,
                    getRequestUrl(dynamicUri),
                    requestBody,
                    response.getResponseStr(),
                    response.isError(),
                    request.getMaskPiiEnabled()
            );
        }
    }

    default void auditAsync(AuditableRequest request, AuditableResponse response, String dynamicUri, String requestBody, Date startTime) {
        CompletableFuture.runAsync(() -> audit(request, response, dynamicUri, requestBody, startTime));
    }

}
