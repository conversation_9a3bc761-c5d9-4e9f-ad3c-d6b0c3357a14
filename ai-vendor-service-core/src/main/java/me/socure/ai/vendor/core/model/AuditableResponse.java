////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2024 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.model;

/**
 * Interface for auditable response objects.
 */
public interface AuditableResponse {

    public String getResponseStr();

    public Boolean isError();

}
