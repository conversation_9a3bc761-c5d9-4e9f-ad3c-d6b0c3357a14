package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("zip5Resolver")
public class Zip5CustomFunctionImpl implements CustomFunction<String> {
    @Override
    public String executeCustomFunction(List<String> inputElements, Config config) {
        String zip = inputElements.get(0);
        if (zip == null) {
            return null;
        }
        return zip.substring(0, Math.min(zip.length(), 5));
    }
}
