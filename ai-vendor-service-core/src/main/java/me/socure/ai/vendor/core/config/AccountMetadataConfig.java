package me.socure.ai.vendor.core.config;

import com.typesafe.config.Config;
import me.socure.account.client.dashboard.AccountMetadataClient;
import me.socure.account.client.dashboard.AccountMetadataClientFactory$;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Scope;
import scala.concurrent.ExecutionContext;

public class AccountMetadataConfig {
    @Autowired
    private Config config;

    @Autowired
    private ExecutionContext executionContext;

    @Bean
    @Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
    public AccountMetadataClient accountMetadataClient() {
        return AccountMetadataClientFactory$.MODULE$.create(config, executionContext);
    }
}
