////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.utils;

import java.util.function.Supplier;

import lombok.AllArgsConstructor;
import me.socure.common.metrics.JavaMetrics;

/**
 * Utility class for metrics.
 */
@AllArgsConstructor
public class AiVendorServiceMetrics {

    private final JavaMetrics metrics;

    public void time(String key, long duration, String... tags) {
        metrics.time(key, duration , tags);
    }

}
