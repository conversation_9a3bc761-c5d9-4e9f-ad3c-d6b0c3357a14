////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;
import org.springframework.http.HttpStatusCode;

/**
 * Vendor Response Details
 */

@Data
@Jacksonized
@Builder
public class VendorCallResponse<T> implements AuditableResponse {

    private int status;
    private String vendorName;
    private T response;

    @Override
    @JsonIgnore
    public String getResponseStr() {
        return response != null ?  response.toString() : "";
    }

    @Override
    @JsonIgnore
    public Boolean isError() {

        return !HttpStatusCode.valueOf(status).is2xxSuccessful();
    }

}
