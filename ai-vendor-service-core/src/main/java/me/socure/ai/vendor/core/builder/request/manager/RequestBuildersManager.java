////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.builder.request.manager;

import me.socure.ai.vendor.core.builder.request.model.RequestBuilder;
import me.socure.ai.vendor.core.builder.request.HttpRequestBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages request builders.
 */

@Component
public class RequestBuildersManager {

    private final Map<String, RequestBuilder> requestBuildersMap = new ConcurrentHashMap<>();

    @Autowired
    public RequestBuildersManager(HttpRequestBuilder httpRequestBuilder) {
        requestBuildersMap.put("http_request_builder", httpRequestBuilder);
    }

    public RequestBuilder getRequestBuilder(String name) {
        return requestBuildersMap.get(name);
    }

}
