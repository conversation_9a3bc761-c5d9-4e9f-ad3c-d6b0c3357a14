package me.socure.ai.vendor.core.builder.request.provider;

import java.util.Map;
import java.util.Optional;

import me.socure.ai.vendor.common.exception.UnsupportedCustomFunctionException;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CustomFunctionProvider {

    private final Map<String, CustomFunction<?>> customFunctionMap;

    @Autowired
    public CustomFunctionProvider(Map<String, CustomFunction<?>> customFunctionMap) {
        this.customFunctionMap = customFunctionMap;
    }

    public CustomFunction<?> getCustomFunction(String functionName) throws UnsupportedCustomFunctionException {
        return Optional.ofNullable(customFunctionMap.get(functionName))
                .orElseThrow(() -> new UnsupportedCustomFunctionException("No Implementation found for custom function name " + functionName));
    }

}
