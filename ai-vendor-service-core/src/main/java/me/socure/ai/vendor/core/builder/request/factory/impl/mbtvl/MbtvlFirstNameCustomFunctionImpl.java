package me.socure.ai.vendor.core.builder.request.factory.impl.mbtvl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("mbtvlFirstName")
public class MbtvlFirstNameCustomFunctionImpl implements CustomFunction<String> {

    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String businessName = inputElements.get(0);
        String firstName = inputElements.get(1);
        if (businessName == null || businessName.isEmpty()) {
            return firstName==null || firstName.isEmpty() ? "" : firstName;
        }
        int busNameMaxLength = 32;
        return businessName.substring(0, Math.min(busNameMaxLength, businessName.length()));

    }
}
