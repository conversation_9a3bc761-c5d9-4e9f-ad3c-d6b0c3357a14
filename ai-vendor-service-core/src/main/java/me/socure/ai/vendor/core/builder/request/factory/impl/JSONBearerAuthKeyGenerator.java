package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component("jsonCacheableBearerAuthKeyGenerator")
public class JSONBearerAuthKeyGenerator extends BearerAuthKeyGeneratorCustomFunctionImpl {

    private final ObjectMapper objectMapper;

    @Autowired
    public JSONBearerAuthKeyGenerator(ObjectMapper objectMapper){
        this.objectMapper = objectMapper;
    }

    @Override
    protected JsonNode convertToJsonNode(ResponseEntity<String> entity) throws IOException {
        return objectMapper.readValue(entity.getBody(), JsonNode.class);
    }
}
