package me.socure.ai.vendor.core.builder.request.factory.impl;

import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.typesafe.config.Config;

@Component("streetAddressResolver")
public class StreetAddressCustomFunctionImpl implements CustomFunction<String> {
	@Override public String executeCustomFunction(List<String> inputElements, Config config) {
		String streetNumber = inputElements.get(0);
		String streetName = inputElements.get(1);
		String streetAddress = inputElements.get(2);
		if(StringUtils.isNotBlank(streetNumber) && StringUtils.isNotBlank(streetName))
			return streetNumber.concat(" ").concat(streetName);
		else
			return streetAddress;
	}
}
