package me.socure.ai.vendor.core.builder.request.factory.impl.bnyvl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("bnyvlGetInquiryType")
public class BnyvlInquiryTypeCustomFunction implements CustomFunction<String> {

    // inquiries should be comma separated
    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String bnyInquiryType = "";
        String inquires  = inputElements.get(0);
        String[] inquiryArr = inquires.split(",");
        for (String inqType : inquiryArr) {
            if (InquiryType.isInquiryType(inqType)) {
                InquiryType inquiryType = InquiryType.valueOf(inqType);
                bnyInquiryType = bnyInquiryType.isEmpty() ? inquiryTypeMap.get(inquiryType) : inquiryTypeMap.get(InquiryType.BOTH);
            }
        }

        return bnyInquiryType;
    }

    private enum InquiryType {
        OWNERSHIP, AVAILABILITY, BOTH;


        private static boolean isInquiryType(String s) {
            for (InquiryType type : values()) {
                if (type.name().equals(s)) {
                    return true;
                }
            }
            return false;
        }
    }

    private static final Map<InquiryType, String> inquiryTypeMap = Map.of(
            InquiryType.OWNERSHIP, "AOA",
            InquiryType.AVAILABILITY, "ASV",
            InquiryType.BOTH, "AOV"
    );
}