package me.socure.ai.vendor.core.builder.request.factory.impl.bnyvl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("bnyvlAcceptanceCriteria")
public class BnyvlAcceptanceCriteriaCustomFunction implements CustomFunction<String> {

    private enum AcceptanceCriteria {
        LOW, MEDIUM, HIGH;

        public static AcceptanceCriteria fromName(String name) {
            for (AcceptanceCriteria criteria : values()) {
                if (criteria.name().equalsIgnoreCase(name)) {
                    return criteria;
                }
            }
            throw new IllegalArgumentException("Unknown AcceptanceCriteria: " + name);
        }
    }

    private static final Map<AcceptanceCriteria, String> acceptanceCriteriaMap = Map.of(
            AcceptanceCriteria.LOW, "3899100001",
            AcceptanceCriteria.MEDIUM, "3899100002",
            AcceptanceCriteria.HIGH, "3899100003"
    );

    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String requestAcceptanceCriteria = inputElements.get(0);
        String profileId;
        if (requestAcceptanceCriteria == null || requestAcceptanceCriteria.isEmpty()) {
            profileId = acceptanceCriteriaMap.get(AcceptanceCriteria.MEDIUM);
        } else {
            profileId = acceptanceCriteriaMap.get(AcceptanceCriteria.fromName(requestAcceptanceCriteria));
        }

        return profileId;
    }
}