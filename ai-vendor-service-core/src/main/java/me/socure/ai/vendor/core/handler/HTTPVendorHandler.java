////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.handler;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import me.socure.ai.vendor.core.builder.request.model.HttpRequest;
import me.socure.ai.vendor.core.builder.request.model.RequestBuilder;
import me.socure.ai.vendor.core.builder.response.model.ResponseBuilder;
import me.socure.ai.vendor.core.model.VendorCallResponse;
import me.socure.ai.vendor.core.utils.HttpRequestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.reactive.function.client.WebClient;

import com.typesafe.config.Config;

import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.core.handler.model.VendorHandler;
import reactor.core.publisher.Mono;

/**
 * Handler for HTTP vendors.
 */
@Log4j2
public class HTTPVendorHandler extends VendorHandler<Mono<HttpRequest>, Mono<VendorCallResponse<?>>> {

    private final WebClient webClient;
    private final long defaultTimeout;
    private final String requestUrl;

	public HTTPVendorHandler(WebClient webClient, RequestBuilder<Mono<HttpRequest>> httpRequestBuilder,
                             ResponseBuilder<Mono<VendorCallResponse<?>>> httpResponseBuilder, Config vendorConfig, TPAuditHandler tpAuditHandler) {
		super(httpRequestBuilder, httpResponseBuilder, vendorConfig, tpAuditHandler);
		this.webClient = webClient;
		this.defaultTimeout = (vendorConfig.hasPath("configs.default.sla.timeout")) ? vendorConfig.getLong("configs.default.sla.timeout") : 60000L;
		this.requestUrl = vendorConfig.hasPath("connection.endpoint") ? vendorConfig.getString("connection.endpoint") + vendorConfig.getString("connection.uri") : "";
	}

    @Override
	protected Mono<VendorCallResponse<?>> processRequest(
		String vendorName,
		Mono<HttpRequest> requestMono,
		Map<String, String> requestConfigs,
		String transactionId,
		Long accountId
	) {
		String uriFromRequestConfig = Objects.nonNull(requestConfigs) && StringUtils.isNotEmpty(requestConfigs.get("uri")) ? requestConfigs.get("uri") : "";
		return
			requestMono.flatMap(
				request -> {
					Mono<ResponseEntity<String>> responseEntityMono = webClient
						.method(HttpRequestUtils.getHttpMethod(requestConfigs))
						.uri(uriBuilder -> uriBuilder.path(uriFromRequestConfig).build())
						.headers(httpHeaders -> httpHeaders.addAll(HttpRequestUtils.getHttpHeaders(request.getHeaders())))
						.bodyValue(request.getBody())
						.retrieve()
						.toEntity(String.class)
						.timeout(Duration.ofMillis(getTimeout(requestConfigs)));
					return responseEntityMono
						.map(entity -> VendorCallResponse.builder()
							.status(entity.getStatusCode().value())
							.vendorName(vendorName)
							.response(entity.getBody())
							.build())
						.onErrorResume(Throwable.class, t -> Mono.just(VendorCallResponse.builder()
							.status(HttpRequestUtils.computeHttpStatus(t))
							.vendorName(vendorName)
							.response(HttpRequestUtils.getErrorMessage(vendorName, t, transactionId, accountId))
							.build()));
				});
	}

	@Override
	public String getRequestUrl(String dynamicUri) {
		return requestUrl + dynamicUri;
	}

    private Long getTimeout(Map<String, String> requestConfigs) {
        if (requestConfigs != null) {
            return Optional.ofNullable(requestConfigs.get("timeout"))
                    .map(Long::parseLong)
                    .orElse(defaultTimeout);
        }
        return defaultTimeout;
    }
}