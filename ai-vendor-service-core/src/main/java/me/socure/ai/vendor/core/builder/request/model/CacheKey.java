package me.socure.ai.vendor.core.builder.request.model;

import lombok.Builder;
import lombok.Getter;

import java.util.List;
import java.util.Objects;

import com.typesafe.config.Config;

@Builder
@Getter
public class CacheKey {
	private final String cacheKeyName;
	private final List<String> metaData;
	private final Config config;
	private final Long createdTime;
	private final Long refreshTime;

	// Method to create a new CacheKey with updated createdTime and refreshTime
	public CacheKey withUpdatedTimestamps(Long newCreatedTime, Long newRefreshTime) {
		return CacheKey.builder()
			.cacheKeyName(this.cacheKeyName)
			.metaData(this.metaData)
			.config(this.config)
			.createdTime(newCreatedTime)
			.refreshTime(newRefreshTime)
			.build();
	}

	@Override
	public boolean equals(Object o) {
		if(this == o)
			return true;
		if(o == null || getClass() != o.getClass())
			return false;
		CacheKey cacheKey = (CacheK<PERSON>) o;
		return Objects.equals(cacheKeyName, cacheKey.cacheKeyName);
	}

	@Override
	public int hashCode() {
		return Objects.hash(cacheKeyName);
	}
}