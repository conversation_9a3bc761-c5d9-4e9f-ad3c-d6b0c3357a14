package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import me.socure.ai.vendor.core.utils.SecretValueProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("secretKeyFunctionImpl")
public class SecretKeyFetchFunctionImpl implements CustomFunction<String> {

    SecretValueProvider secretValueProvider;
    ObjectMapper mapper;
    @Autowired
    public SecretKeyFetchFunctionImpl(SecretValueProvider secretValueProvider, ObjectMapper mapper) {
        this.secretValueProvider = secretValueProvider;
        this.mapper = mapper;
    }
    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String awsKeyPath = inputElements.get(0);
        String keyName = inputElements.get(1);
        try {
            Map<String, String> secretMap = mapper.readValue(secretValueProvider.getSecretValue(awsKeyPath), Map.class);
            return secretMap.get(keyName);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}