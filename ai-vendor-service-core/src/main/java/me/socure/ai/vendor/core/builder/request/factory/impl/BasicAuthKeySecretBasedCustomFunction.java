package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import me.socure.ai.vendor.core.builder.request.model.TokenGenerateResponse;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;
import java.util.Map;

import me.socure.ai.vendor.core.utils.SecretValueProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.typesafe.config.Config;

@Component("cacheableBasicAuthKeySecretBased")
public class BasicAuthKeySecretBasedCustomFunction extends CacheableAuthKeyGeneratorCustomFunctionImpl {

    private final SecretValueProvider secretValueProvider;
    private final ObjectMapper mapper;

    @Autowired
    public BasicAuthKeySecretBasedCustomFunction(SecretValueProvider secretValueProvider, ObjectMapper mapper){
        this.secretValueProvider = secretValueProvider;
        this.mapper = mapper;
    }

    @Override
    public TokenGenerateResponse getAuthValue(List<String> inputElements, Config config) {
        try {
            String keyPath = inputElements.get(3);
            String keyName = inputElements.get(4);
            Map<String, String> secretMap = mapper.readValue(secretValueProvider.getSecretValue(keyPath), Map.class);
            String secretKey = secretMap.get(keyName);
            String token = "Basic " + Base64.getEncoder().encodeToString(secretKey.getBytes(StandardCharsets.UTF_8));
            return TokenGenerateResponse.builder().token(token).build();
        } catch (Exception e){
            throw new RuntimeException("Error while fetching secret key", e);
        }
    }
}