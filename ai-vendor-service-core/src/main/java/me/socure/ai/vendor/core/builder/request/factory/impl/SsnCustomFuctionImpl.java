package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("ssnCustomFunction")
public class SsnCustomFuctionImpl implements CustomFunction<String> {

    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String businessName = inputElements.get(0);
        String ein = inputElements.get(1);
        String nationalId = inputElements.get(2);
        return (businessName != null && !businessName.isEmpty()) ? ein : nationalId;
    }
}
