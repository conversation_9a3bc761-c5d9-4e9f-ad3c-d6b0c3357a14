////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2023 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.builder.request.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.typesafe.config.Config;
import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.core.model.VendorCallRequest;
import me.socure.ai.vendor.common.exception.UnsupportedCustomFunctionException;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import me.socure.ai.vendor.core.builder.request.provider.CustomFunctionProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.*;
import java.util.stream.Collectors;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * Utility class for using request templates.
 */
@Log4j2
@Component
public class RequestTemplateHandler {

    private static final String FROM_INPUT = "input";
    private static final String FROM_CONFIG = "config";
    private static final String FROM_REQUEST_CONFIG = "requestConfig";
    private static final String FROM_TEMPLATE = "template";
    private static final String CUSTOM_FUNCTION = "function";

    private final ObjectMapper objectMapper;

    private final CustomFunctionProvider customFunctionProvider;

    private final Map<String, String> requestTemplateMap;

    private final Map<String, String> requestTemplateTypeMap;

    private static final List<String> prefixList = Arrays.asList("input", "config", "template", "function", "requestConfig");

    @Autowired
    public RequestTemplateHandler(ObjectMapper objectMapper, CustomFunctionProvider customFunctionProvider) {
        this.objectMapper = objectMapper;
        this.customFunctionProvider = customFunctionProvider;
        try {
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = Arrays.stream(resolver.getResources("http_request_templates/**")).filter(Resource::isReadable).toArray(Resource[]::new);
            requestTemplateMap = Arrays.stream(resources).collect(Collectors.toMap(this::getTemplateName, this::getAsString));
            requestTemplateTypeMap = Arrays.stream(resources).collect(Collectors.toMap(this::getTemplateName, this::getFileExtension));
        } catch (Exception e) {
            throw new RuntimeException("Error while loading HTTP request templates.", e);
        }
    }

    private String getTemplateName(Resource resource) {
        try {
            String path = resource.getURI().toString();
            // Remove the initial part of the path up to "http_request_templates/"
            path = path.substring(path.indexOf("http_request_templates/") + "http_request_templates/".length());
            // Replace '/' with '.'
            path = path.replace('/', '.');
            int lastDotIndex = path.lastIndexOf(".");
            return (lastDotIndex == -1) ? path : path.substring(0, lastDotIndex);
        } catch (Exception e) {
            throw new RuntimeException("Error while generating key for resource: " + resource, e);
        }
    }

    private String getFileExtension(Resource resource) {
        try {
            String path = resource.getURI().toString();
            // Remove the initial part of the path up to "http_request_templates/"
            path = path.substring(path.indexOf("http_request_templates/") + "http_request_templates/".length());
            // Replace '/' with '.'
            path = path.replace('/', '.');
            int lastDotIndex = path.lastIndexOf(".");
            return (lastDotIndex == -1) ? "" : path.substring(lastDotIndex+1).toUpperCase();
        } catch (Exception e) {
            throw new RuntimeException("Error while generating key for resource: " + resource, e);
        }
    }

    private String getAsString(Resource resource) {
        try {
            Reader reader = new InputStreamReader(resource.getInputStream(), UTF_8);
            return FileCopyUtils.copyToString(reader);
        } catch (Exception e) {
            throw new RuntimeException("Error while loading HTTP request template - " + resource.getFilename(), e);
        }
    }

    public String buildRequest(String templateName, VendorCallRequest requestData, Config config) {
        try {
            Map<String, Object> requestMap = objectMapper.convertValue(requestData.getPiis(), new TypeReference<HashMap<String, Object>>() {
            });
            if(Objects.nonNull(requestMap)) {
                requestMap.put("transactionId", requestData.getTransactionId());
                requestMap.put("accountId", requestData.getAccountId());
                requestMap.put("maskPii", requestData.getMaskPiiEnabled());
                if(Objects.nonNull(requestData.getVendorConfig()))
                    requestMap.put("requestConfig", requestData.getVendorConfig());
            }
            return buildRequest(templateName, requestMap, config);
        }
        catch(UnsupportedCustomFunctionException uce) {
            throw new RuntimeException("UnsupportedCustomFunctionException occurred with message " + uce.getMessage(), uce);
        }
        catch(Exception e) {
            throw new RuntimeException("Error while building request from template -" + templateName + " with message " + e.getMessage(), e);
        }
    }

    private String buildRequest(String templateName, Map<String, Object> requestMap, Config config) throws JsonProcessingException, UnsupportedCustomFunctionException {
        String templateStr = Optional.ofNullable(requestTemplateMap.get(templateName)).orElseThrow();
        String templateType = Optional.ofNullable(requestTemplateTypeMap.get(templateName)).orElseThrow();
		return getRequest(templateStr,templateType, requestMap, config);
    }

    private String getRequest(String templateStr, String templateType, Map<String, Object> requestMap, Config config) throws JsonProcessingException, UnsupportedCustomFunctionException {
        switch (templateType) {
            case "JSON": {
                ObjectNode templateObject = objectMapper.readValue(templateStr, ObjectNode.class);
                return getJSONRequest(templateObject, requestMap, config);
            }
            case "XML": {
                return getXMLRequest(templateStr, requestMap, config);
            }
            default: throw new RuntimeException("Invalid File Extension Type to buildRequest in RequestTemplateHandler");
        }
    }

    private String getXMLRequest(String templateStr, Map<String, Object> requestMap, Config config) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder documentBuilder = factory.newDocumentBuilder();
            Document document = documentBuilder.parse(new InputSource(new StringReader(templateStr)));
            Node rootNode = document.getDocumentElement();
            updateNodeWithValues(rootNode,requestMap,config);
            return documentToString(document);
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred when building XML Request with message "+ex.getMessage(), ex);
        }
    }

    private void updateNodeWithValues(Node node, Map<String, Object> requestMap, Config config) throws JsonProcessingException, UnsupportedCustomFunctionException {
        NodeList nodeList = node.getChildNodes();
        for(int i =0; i<nodeList.getLength(); i++) {
            Node childNode = nodeList.item(i);
            if(childNode.getNodeType() == Node.ELEMENT_NODE) {
                updateNodeWithValues(childNode, requestMap, config);
            } else if(childNode.getNodeType() == Node.TEXT_NODE) {
                String stringValue = getFieldValue(requestMap, childNode.getTextContent(), config).toString();
                childNode.setTextContent(stringValue);
            }
        }
    }

    private String documentToString(Document doc) {
        DOMSource domSource = new DOMSource(doc);
        StringWriter stringWriter = new StringWriter();
        StreamResult result = new StreamResult(stringWriter);
        TransformerFactory tf = TransformerFactory.newInstance();
        try {
            Transformer transformer = tf.newTransformer();
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
            transformer.setOutputProperty(OutputKeys.METHOD, "xml");
            transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
            transformer.transform(domSource, result);
            return stringWriter.getBuffer().toString();
        } catch (Exception e) {
            throw new RuntimeException("Exception occurred when converting XML Document to String with message "+e.getMessage(), e);
        }
    }

    private String getJSONRequest(ObjectNode objNode, Map<String, Object> requestMap, Config config) throws JsonProcessingException, UnsupportedCustomFunctionException {
        List<String> fieldNames = new LinkedList<>();
        objNode.fieldNames().forEachRemaining(fieldNames::add);
        for(String fieldName : fieldNames) {
            JsonNode node = objNode.get(fieldName);
            if(node.getNodeType().equals(JsonNodeType.STRING)) {
                String fieldValue = node.textValue();
                if(containsAnyPrefix(fieldValue)) {
                    Object res = getFieldValue(requestMap, fieldValue, config);
                    if(res == null || (res instanceof String && (res.toString().trim().isEmpty())))
                        objNode.remove(fieldName);
                    else {
                        if(res instanceof Boolean)
                            objNode.put(fieldName, Boolean.valueOf(res.toString()));
                        else if(res instanceof Long)
                            objNode.put(fieldName, Long.valueOf(res.toString()));
                        else if(res instanceof JsonNode)
                            objNode.set(fieldName, (JsonNode) res);
                        else
                            objNode.put(fieldName, res.toString());
                    }
                }
                else if(fieldValue.isEmpty()) {
                    objNode.remove(fieldName);
                }
            }
            else if(node.getNodeType().equals(JsonNodeType.OBJECT)) {
                ObjectNode obj = (ObjectNode) node;
                String result = getJSONRequest(obj, requestMap, config);
                ObjectNode json = objectMapper.readValue(result, ObjectNode.class);
                objNode.put(fieldName, json);
            }

            // Handle Array nodes recursively
            else if (node.getNodeType().equals(JsonNodeType.ARRAY)) {
                ArrayNode arrayNode = (ArrayNode) node;
                for (int i = 0; i < arrayNode.size(); i++) {
                    JsonNode arrayItem = arrayNode.get(i);
                    if (arrayItem.getNodeType().equals(JsonNodeType.OBJECT)) {
                        ObjectNode arrayObject = (ObjectNode) arrayItem;
                        String result = getJSONRequest(arrayObject, requestMap, config);
                        ObjectNode json = objectMapper.readValue(result, ObjectNode.class);
                        arrayNode.set(i, json);
                    } else if (arrayItem.getNodeType().equals(JsonNodeType.STRING)) {
                        String fieldValue = arrayItem.textValue();
                        if (containsAnyPrefix(fieldValue)) {
                            Object res = getFieldValue(requestMap, fieldValue, config);
                            if (res instanceof JsonNode) {
                                arrayNode.set(i, (JsonNode) res);
                            } else {
                                arrayNode.set(i, objectMapper.convertValue(res, JsonNode.class));
                            }
                        }
                    }
                }
            }
        }
        return objectMapper.writeValueAsString(objNode);
    }

    private Object getFieldValue(Map<String, Object> requestMap, String fieldValue, Config config) throws JsonProcessingException, UnsupportedCustomFunctionException {
        int indexValue = fieldValue.indexOf(".");

        if(indexValue == -1)
            return fieldValue;
        String prefix = fieldValue.substring(0, indexValue);
        return switch(prefix) {
            case FROM_INPUT -> {
                final Object value = requestMap.get(fieldValue.substring(indexValue + 1));
                yield value != null ? value : "";
            }
            case FROM_CONFIG -> config.getString(fieldValue.substring(indexValue + 1));
            case FROM_REQUEST_CONFIG -> {
                Object value = null;
                if(requestMap.containsKey("requestConfig"))
                    value = ((HashMap<String, String>) requestMap.get("requestConfig")).get(fieldValue.substring(indexValue + 1));
                yield value != null ? value : "";
            }
            case FROM_TEMPLATE -> buildRequest(fieldValue.substring(indexValue + 1), requestMap, config);
            case CUSTOM_FUNCTION -> {
                String[] args = fieldValue.split(" ");
                int index = args[0].indexOf(".");
                String functionName = args[0].substring(index + 1);
                CustomFunction customFunctionFactory = customFunctionProvider.getCustomFunction(functionName);
                List<Object> processedInputElements = new ArrayList<>();
                for(int i = 1; i < args.length; i++) {
                    processedInputElements.add(getFieldValue(requestMap, args[i], config));
                }
                yield customFunctionFactory.executeCustomFunction(processedInputElements, config);
            }
            default -> throw new IllegalStateException("Unexpected value: " + prefix);
        };
    }

    public static boolean containsAnyPrefix(String fieldValue) {
        return prefixList.stream()
                .anyMatch(fieldValue::contains);
    }
}
