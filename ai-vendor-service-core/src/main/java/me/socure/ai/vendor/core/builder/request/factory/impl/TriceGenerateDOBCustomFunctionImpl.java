package me.socure.ai.vendor.core.builder.request.factory.impl;

import lombok.extern.log4j.Log4j2;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.typesafe.config.Config;

@Component("triceDOBGenerator")
@Log4j2
public class TriceGenerateDOBCustomFunctionImpl implements CustomFunction<String> {

	ObjectMapper objectMapper;

	//inputElements(0) should be Date String,
	//inputElements(1) Second be Format in which the Date is
	//Output would be a json like below,
	//{
	//	"day": 12,
	//	"month": 2,
	//  "year": 1994
	//}
	@Autowired
	public TriceGenerateDOBCustomFunctionImpl(ObjectMapper objectMapper) {
		this.objectMapper = objectMapper;
	}

	@Override
	public Object executeCustomFunction(List<String> inputElements, Config config) {
		String dateString = inputElements.get(0);
		String currentFormat = inputElements.get(1);
		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(currentFormat);
			LocalDate date = LocalDate.parse(dateString, formatter);

			int day = date.getDayOfMonth();
			int month = date.getMonthValue();
			int year = date.getYear();

			ObjectNode jsonNode = objectMapper.createObjectNode();

			jsonNode.put("day", day);
			jsonNode.put("month", month);
			jsonNode.put("year", year);
			return jsonNode;
		}
		catch(Exception ex) {
			log.error("Exception occurred in triceDOBGenerator for date: {}, format: {} due to ", dateString, currentFormat, ex);
			return null;
		}
	}
}