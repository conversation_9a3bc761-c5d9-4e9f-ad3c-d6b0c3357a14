package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("digitExtractor")
public class DigitExtractorCustomFunctionImpl implements CustomFunction<String> {

    @Override
    public Object executeCustomFunction(List<String> inputElements, Config config) {
        String input = inputElements.get(0);
        return input != null ? input.replaceAll("[^0-9]", "") : "";
    }
}
