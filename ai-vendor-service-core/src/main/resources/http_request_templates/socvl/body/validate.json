{"firstName": "input.firstName", "surName": "input.surName", "streetAddress": "function.mergeAddressResolver input.streetAddress input.streetAddress2", "city": "input.city", "state": "input.state", "zip": "function.zip5Resolver input.zipCode", "mobileNumber": "function.mobNumberExtract input.mobileNumber", "nationalId": "input.nationalId", "dob": "input.dob", "email": "input.email", "showDocs": "config.configs.idResolution.staticApiParams.showDocs", "displayMethod": "config.configs.idResolution.staticApiParams.displayMethod", "searchBySoundex": "config.configs.idResolution.staticApiParams.searchBySoundex", "fuzzyDobSearch": "config.configs.idResolution.staticApiParams.fuzzyDobSearch", "compareMethod": "config.configs.idResolution.staticApiParams.compareMethod", "apiVersion": "config.configs.idResolution.staticApiParams.apiVersion", "thresholdScore": "config.configs.idResolution.staticApiParams.thresholdScore", "maxResultRecords": "config.configs.idResolution.staticApiParams.maxResultRecords", "matchLevel": "config.configs.idResolution.staticApiParams.matchLevel", "connectEnv": "config.configs.idResolution.staticApiParams.connectEnv", "showIndexes": "config.configs.idResolution.staticApiParams.showIndexes"}