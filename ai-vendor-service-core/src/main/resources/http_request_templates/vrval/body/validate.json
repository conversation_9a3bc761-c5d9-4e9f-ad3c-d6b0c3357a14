{"accountId": "input.accountId", "transactionId": "input.transactionId", "firstName": "input.firstName", "surName": "input.surName", "streetAddress": "input.streetAddress", "city": "input.city", "state": "input.state", "zipCode": "input.zipCode", "mobileNumber": "input.mobileNumber", "nationalId": "input.nationalId", "dob": "input.dob", "email": "input.email", "preferencesKyc": {"exactDob": true, "dobMatchLogic": "exact_yyyy_mm_dd", "exactSSN": true, "nationalIdMatchLogic": "exact"}, "customPreferencesKyc": {"maxAddressCount": 3, "maxEmailCount": 3, "maxPhoneCount": 3}, "preferencesEntity": {"exactDob": true, "dobMatchLogic": "exact_yyyy_mm_dd", "exactSSN": true, "nationalIdMatchLogic": "exact"}, "maskPii": "input.maskPii", "workflows": ["entity"], "submissionDate": "requestConfig.submissionDate", "modulesEnabled": ["Deceased<PERSON><PERSON>ck"], "mergeEntityConfig": {"enabled": true, "overallLimit": 30, "vendorLimit": 20, "timeLimitInMillis": 200}, "rootNonPartnerAccountId": "input.accountId"}