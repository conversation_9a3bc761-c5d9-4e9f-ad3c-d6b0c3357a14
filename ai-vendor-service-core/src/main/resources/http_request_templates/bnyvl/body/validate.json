{"metadata": {"requestId": "input.transactionId", "signatureAlgorithm": "SHA256withRSA", "signature": "function.bynvlPayloadSign template.bnyvl.body.validate-request"}, "request": {"numberOfInquiries": 1, "accountValidationRequest": [{"profileId": "function.bnyvlAcceptanceCriteria requestConfig.acceptanceCriteria", "addId": "function.bnyvlAddId requestConfig.accountId", "inquiryType": "function.bnyvlGetInquiryType requestConfig.inquiries", "requestContext": "DEP", "rtn": "input.routingNumber", "accountNumber": "input.accountNumber", "feeAttrib": "HH", "fName": "input.firstName", "lName": "input.surName", "mName": "", "namePfx": "", "nameSfx": "", "ssn": "function.ssnCustomFunction input.businessName input.ein input.nationalId", "hmPhone": "function.mobNumberExtract input.mobileNumber", "wkPhone": "function.mobNumberExtract input.mobileNumber", "address1": "input.streetAddress", "address2": "input.streetAddress2", "busName": "input.businessName", "city": "input.city", "state": "input.state", "zip": "input.zipCode", "dob": "function.digitExtractor input.dob", "idNumber": "", "idState": "", "idType": ""}]}}