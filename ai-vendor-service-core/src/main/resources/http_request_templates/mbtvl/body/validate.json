{"RuleNum": "config.configs.mbtvl.rulenum", "PersonInfo": {"PersonName": {"FirstName": "function.mbtvlFirstName input.businessName input.firstName", "LastName": "function.mbtvlLastName input.businessName input.surName"}, "ContactInfo": {"PhoneNum": {"Phone": "function.mobNumberExtract input.mobileNumber"}, "PostAddr": {"Addr1": "input.streetAddress", "City": "input.city", "StateProv": "input.state", "PostalCode": "input.zipCode"}}, "TINInfo": {"TINType": "function.mbtvlTinType input.businessName", "TaxId": "function.ssnCustomFunction input.businessName input.ein input.nationalId"}}, "BankAccount": {"RoutingNumber": "input.routingNumber", "AccountNum": "input.accountNumber"}}