{"amount": 1, "enrollment": "config.configs.trice.enrollment.id", "ultimate_sending_party": "requestConfig.ultimateSendingPartyId", "internal_description": "input.transactionId", "memo": "requestConfig.memo", "new_party": {"address": "function.triceAddressGenerator input.streetAddress input.streetAddress2 input.city input.state input.country input.zipCode", "bank_details": {"account_holder_name": "function.fullNameResolver input.entityName input.firstName input.middleName input.surName", "account_holder_type": "requestConfig.accountHolderType", "account_number": "input.accountNumber", "routing_number": "input.routingNumber"}, "can_receive": true, "name": "function.fullNameResolver input.entityName input.firstName input.middleName input.surName", "email": "input.email", "dob": "function.triceDOBGenerator input.dob yyyyMMdd", "domestic": "requestConfig.domestic"}}