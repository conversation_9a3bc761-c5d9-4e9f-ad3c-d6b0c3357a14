package me.socure.ai.vendor.core.builder.request.factory.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;
import me.socure.ai.vendor.core.builder.request.model.CacheKey;
import me.socure.ai.vendor.core.utils.HttpRequestUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BearerAuthKeyGeneratorCustomFunctionImplTest {
	@Mock
	private Config config;

	@Mock
	private Config connectionConfig;

	@Mock
	private WebClient webClient;

	@Mock
	private WebClient.RequestHeadersSpec requestHeadersSpec;

	@Mock
	private WebClient.RequestBodyUriSpec requestBodyUriSpec;

	@Mock
	private WebClient.RequestBodySpec requestBodySpec;

	@Mock
	private WebClient.ResponseSpec responseSpec;

	private MockedStatic<HttpRequestUtils> mockedHttpRequestUtils;

    private CacheableAuthKeyGeneratorCustomFunctionImpl jsonBearerAuthKeyGenerator;
    private CacheableAuthKeyGeneratorCustomFunctionImpl xmlBearerAuthKeyGenerator;


    @BeforeEach
	public void setUp() {
		mockedHttpRequestUtils = mockStatic(HttpRequestUtils.class);
        jsonBearerAuthKeyGenerator = new JSONBearerAuthKeyGenerator(new ObjectMapper());
        xmlBearerAuthKeyGenerator = new XMLBearerAuthKeyGenerator(new ObjectMapper());
	}

	@AfterEach
	public void tearDown() {
		mockedHttpRequestUtils.close();
	}

	@Test
	public void testGetAuthValue() {
		List<String> inputElements = Arrays.asList("bearer", "dataZooSessionToken", "21600000", "MockedBody", "MockedHeaders", "/sessionToken");
		String vendorName = "testVendor";
		String endpoint = "http://test-endpoint";
		String authorizationUri = "/auth";

		lenient().when(config.getString("name")).thenReturn(vendorName);
		lenient().when(config.getConfig("connection")).thenReturn(connectionConfig);
		lenient().when(connectionConfig.getString("authorization.endpoint")).thenReturn(endpoint);
		lenient().when(connectionConfig.getString("authorization.uri")).thenReturn(authorizationUri);

		mockedHttpRequestUtils.when(() -> HttpRequestUtils.getWebClient(connectionConfig, endpoint, authorizationUri)).thenReturn(webClient);
		mockedHttpRequestUtils.when(() -> HttpRequestUtils.getHttpHeaders(anyMap())).thenReturn(mock(org.springframework.http.HttpHeaders.class));

		lenient().when(webClient.post()).thenReturn(requestBodyUriSpec);
		lenient().when(requestBodyUriSpec.headers(any())).thenReturn(requestBodySpec);
		lenient().when(requestBodySpec.bodyValue(any())).thenReturn(requestHeadersSpec);
		lenient().when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);

		String responseBody = "{\n" +
				"    \"sessionToken\" : \"tokenGenerated\"\n" +
				"}";

		ResponseEntity<String> responseEntity = mock(ResponseEntity.class);
		lenient().when(responseEntity.getBody()).thenReturn(responseBody);

		lenient().when(responseSpec.toEntity(String.class)).thenReturn(Mono.just(responseEntity));

		String actualAuthValue = (String) jsonBearerAuthKeyGenerator.executeCustomFunction(inputElements, config);

		CacheKey searchKey = CacheKey.builder().cacheKeyName("dataZooSessionToken").build();
		String authValueFromCache = jsonBearerAuthKeyGenerator.getAuthKeysCache().get(searchKey);

		assertEquals("Bearer tokenGenerated", actualAuthValue);
		assertEquals("Bearer tokenGenerated", authValueFromCache);
	}

	@Test
	public void testGetAuthValueWithError() {
		List<String> inputElements = Arrays.asList("bearer", "dataZooSessionToken", "21600000", "MockedBody", "MockedHeaders", "/sessionToken");
		String vendorName = "testVendor";
		String endpoint = "http://test-endpoint";
		String authorizationUri = "/auth";

		lenient().when(config.getString("name")).thenReturn(vendorName);
		lenient().when(config.getConfig("connection")).thenReturn(connectionConfig);
		lenient().when(connectionConfig.getString("authorization.endpoint")).thenReturn(endpoint);
		lenient().when(connectionConfig.getString("authorization.uri")).thenReturn(authorizationUri);

		mockedHttpRequestUtils.when(() -> HttpRequestUtils.getWebClient(connectionConfig, endpoint, authorizationUri)).thenReturn(webClient);
		mockedHttpRequestUtils.when(() -> HttpRequestUtils.getHttpHeaders(anyMap())).thenReturn(mock(org.springframework.http.HttpHeaders.class));

		lenient().when(webClient.post()).thenReturn(requestBodyUriSpec);
		lenient().when(requestBodyUriSpec.headers(any())).thenReturn(requestBodySpec);
		lenient().when(requestBodySpec.bodyValue(any())).thenReturn(requestHeadersSpec);
		lenient().when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);

		lenient().when(responseSpec.toEntity(String.class)).thenReturn(Mono.error(new RuntimeException("Error")));

		mockedHttpRequestUtils.when(() -> HttpRequestUtils.getErrorMessage(anyString(), any(Throwable.class))).thenReturn("Error");

		String actualAuthValue = (String) jsonBearerAuthKeyGenerator.executeCustomFunction(inputElements, config);

		CacheKey searchKey = CacheKey.builder().cacheKeyName("dataZooSessionToken").build();
		String authValueFromCache = jsonBearerAuthKeyGenerator.getAuthKeysCache().get(searchKey);


        assertEquals("", actualAuthValue);
		assertNull(authValueFromCache);
	}

	@Test
	public void testGetAuthValueFromXML() {
		List<String> inputElements = Arrays.asList("bearer", "equifaxukAccessToken", "21600000", "MockedBody", "MockedHeaders", "/Body/logonResponse/accesstoken");
		String vendorName = "testVendor";
		String endpoint = "http://test-endpoint";
		String authorizationUri = "/auth";

		lenient().when(config.getString("name")).thenReturn(vendorName);
		lenient().when(config.getConfig("connection")).thenReturn(connectionConfig);
		lenient().when(connectionConfig.getString("authorization.endpoint")).thenReturn(endpoint);
		lenient().when(connectionConfig.getString("authorization.uri")).thenReturn(authorizationUri);

		mockedHttpRequestUtils.when(() -> HttpRequestUtils.getWebClient(connectionConfig, endpoint, authorizationUri)).thenReturn(webClient);
		mockedHttpRequestUtils.when(() -> HttpRequestUtils.getHttpHeaders(anyMap())).thenReturn(mock(org.springframework.http.HttpHeaders.class));

		lenient().when(webClient.post()).thenReturn(requestBodyUriSpec);
		lenient().when(requestBodyUriSpec.headers(any())).thenReturn(requestBodySpec);
		lenient().when(requestBodySpec.bodyValue(any())).thenReturn(requestHeadersSpec);
		lenient().when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);

		String responseBody = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
				"    <soapenv:Header/>\n" +
				"    <soapenv:Body>\n" +
				"        <ns2:logonResponse xmlns:ns2=\"http://ewssecurity.services.uk.equifax.com/schema/v4/logon/logonresponse\">\n" +
				"            <accesstoken>tokenGenerated</accesstoken>\n" +
				"        </ns2:logonResponse>\n" +
				"    </soapenv:Body>\n" +
				"</soapenv:Envelope>";

		ResponseEntity<String> responseEntity = mock(ResponseEntity.class);
		lenient().when(responseEntity.getBody()).thenReturn(responseBody);

		lenient().when(responseSpec.toEntity(String.class)).thenReturn(Mono.just(responseEntity));

		String actualAuthValue =  (String) xmlBearerAuthKeyGenerator.executeCustomFunction(inputElements, config);

		CacheKey searchKey = CacheKey.builder().cacheKeyName("equifaxukAccessToken").build();
		String authValueFromCache = xmlBearerAuthKeyGenerator.getAuthKeysCache().get(searchKey);

        assertEquals("Bearer tokenGenerated", actualAuthValue);
		assertEquals("Bearer tokenGenerated", authValueFromCache);
	}
}
