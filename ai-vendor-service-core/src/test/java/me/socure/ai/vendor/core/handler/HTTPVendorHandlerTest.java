package me.socure.ai.vendor.core.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import me.socure.ai.vendor.core.model.VendorCallResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.Header;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

import com.amazonaws.services.s3.Headers;
import com.typesafe.config.ConfigFactory;
import reactor.core.publisher.Mono;

class HTTPVendorHandlerTest {

    private ClientAndServer mockServer;

    @BeforeEach
    void setUp() {
        mockServer = ClientAndServer.startClientAndServer(1080);
    }

    @AfterEach
    void tearDown() {
        mockServer.stop();
    }

    @Test
    void processRequest_SuccessfulResponse() {
        try(MockServerClient serverClient = new MockServerClient("localhost", 1080)) {
            serverClient
                    .when(HttpRequest.request().withMethod("POST"))
                    .respond(HttpResponse.response()
                            .withHeader(Header.header(Headers.CONTENT_TYPE, MediaType.APPLICATION_JSON.toString()))
                            .withStatusCode(200)
                            .withBody("{\"MockServer response\":\"YES\"}"));
            HTTPVendorHandler handler = new HTTPVendorHandler(WebClient.create("http://localhost:1080"), null, null, ConfigFactory.empty(), null);

            String request = "Sample request";
            Map<String, String> requestHeader = Map.of("Content-Type", "application/json");
            Mono<me.socure.ai.vendor.core.builder.request.model.HttpRequest> requestMono = Mono.just(me.socure.ai.vendor.core.builder.request.model.HttpRequest.builder().body(request).headers(requestHeader).build());

            Map<String, String> requestConfigs = Collections.singletonMap("timeout", "10000");

            VendorCallResponse<?> result = handler.processRequest("vendor", requestMono, requestConfigs, "", -1L).block();

            assertNotNull(result);
            assertEquals(200, result.getStatus());
            assertEquals("vendor", result.getVendorName());
            assertEquals("{\"MockServer response\":\"YES\"}", result.getResponse().toString());
        }
    }

    @Test
    void processRequest_FailedResponse() {
        try(MockServerClient serverClient = new MockServerClient("localhost", 1080)) {
            serverClient
                    .when(HttpRequest.request().withPath("/fail").withMethod("POST"))
                    .respond(HttpResponse.response()
                            .withHeader(Header.header(Headers.CONTENT_TYPE, MediaType.APPLICATION_JSON.toString()))
                            .withStatusCode(500)
                            .withBody("{\"MockServer response\":\"YES\"}"));

            HTTPVendorHandler handler = new HTTPVendorHandler(WebClient.create("http://localhost:1080/fail"), null,null, ConfigFactory.empty(), null);

            String request = "Sample request";
            Map<String, String> requestHeader = Map.of("Content-Type", "application/json");
            Mono<me.socure.ai.vendor.core.builder.request.model.HttpRequest> requestMono = Mono.just(me.socure.ai.vendor.core.builder.request.model.HttpRequest.builder().body(request).headers(requestHeader).build());

            Map<String, String> requestConfigs = Collections.singletonMap("timeout", "10000");

            VendorCallResponse<?> result = handler.processRequest("vendor", requestMono, requestConfigs, "", -1L).block();

            assertNotNull(result);
            assertEquals(500, result.getStatus());
            assertEquals("vendor", result.getVendorName());
        }
    }

    @Test
    void processRequest_TimeoutCase() {
        try(MockServerClient serverClient = new MockServerClient("localhost", 1080)) {
            serverClient
                    .when(HttpRequest.request().withPath("/timeout").withMethod("POST"))
                    .respond(HttpResponse.response()
                            .withHeader(Header.header(Headers.CONTENT_TYPE, MediaType.APPLICATION_JSON.toString()))
                            .withStatusCode(200)
                            .withDelay(TimeUnit.SECONDS, 5)
                            .withBody("{\"MockServer response\":\"YES\"}"));

            HTTPVendorHandler handler = new HTTPVendorHandler(WebClient.create("http://localhost:1080/timeout"), null, null, ConfigFactory.empty(), null);

            String request = "Sample request";
            Map<String, String> requestHeader = Map.of("Content-Type", "application/json");
            Mono<me.socure.ai.vendor.core.builder.request.model.HttpRequest> requestMono = Mono.just(me.socure.ai.vendor.core.builder.request.model.HttpRequest.builder().body(request).headers(requestHeader).build());

            Map<String, String> requestConfigs = Collections.singletonMap("timeout", "100");

            VendorCallResponse<?> result = handler.processRequest("vendor", requestMono, requestConfigs, "", -1L).block();

            assertNotNull(result);
            assertEquals(504, result.getStatus());
            assertEquals("vendor", result.getVendorName());
        }
    }

}
