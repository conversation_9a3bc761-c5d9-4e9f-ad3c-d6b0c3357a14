////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2024 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.builder.request;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.HashMap;
import java.util.Map;

import me.socure.ai.vendor.common.model.Piis;
import me.socure.ai.vendor.core.builder.request.factory.CustomFunction;
import me.socure.ai.vendor.core.builder.request.factory.impl.*;
import me.socure.ai.vendor.core.builder.request.factory.impl.bnyvl.BnyvlAcceptanceCriteriaCustomFunction;
import me.socure.ai.vendor.core.builder.request.factory.impl.bnyvl.BnyvlInquiryTypeCustomFunction;
import me.socure.ai.vendor.core.builder.request.provider.CustomFunctionProvider;
import me.socure.ai.vendor.core.builder.request.utils.RequestTemplateHandler;
import me.socure.ai.vendor.core.model.VendorCallRequest;
import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.ConfigFactory;


/**
 * Unit tests for HTTP request builder.
 */

public class HttpRequestBuilderTest {

    public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static final Map<String, CustomFunction<?>> customFunctionMap = Map.of(
            "dateFormatter", new DateFormatterCustomFunctionImpl(),
            "streetAddressResolver", new StreetAddressCustomFunctionImpl(),
            "triceAddressGenerator", new TriceGenerateAddressCustomFunctionImpl(OBJECT_MAPPER),
            "fullNameResolver", new FullNameCustomFunctionImpl(),
            "triceDOBGenerator", new TriceGenerateDOBCustomFunctionImpl(OBJECT_MAPPER),
            "bnyvlGetInquiryType", new BnyvlInquiryTypeCustomFunction(),
            "bnyvlAcceptanceCriteria", new BnyvlAcceptanceCriteriaCustomFunction()
    );

    @Test
    void testBuildWithDefaultTemplate() {
        HttpRequestBuilder httpRequestBuilder = new HttpRequestBuilder(new ObjectMapper(), new RequestTemplateHandler(new ObjectMapper(), new CustomFunctionProvider(new HashMap<>())));
        var configs = new HashMap<String, Object>();
        configs.put("configs.default.template", "default");
        configs.put("configs.licence.key", "default");
        configs.put("configs.default.header.template", "customheaders");


        Map<String, String> vendorConfig = new HashMap<>();
        VendorCallRequest vendorCallRequest = getRequestBuilder().vendorConfig(vendorConfig).build();

        String expected = """
                {"fn":"john","ln":"doe","cid":"default"}""";
        String actual = httpRequestBuilder.build("custom", vendorCallRequest, ConfigFactory.parseMap(configs)).block().getBody();
        assertEquals(expected, actual);
    }

    @Test
    void testBuildWithCustomTemplate() {
        HttpRequestBuilder httpRequestBuilder = new HttpRequestBuilder(new ObjectMapper(), new RequestTemplateHandler(new ObjectMapper(), new CustomFunctionProvider(new HashMap<>())));
        var configs = new HashMap<String, Object>();
        configs.put("configs.default.template", "default");
        configs.put("configs.licence.key", "custom");
        configs.put("configs.default.header.template", "customheaders");

        Map<String, String> vendorConfig = new HashMap<>();
        vendorConfig.put("template", "custom");
        VendorCallRequest vendorCallRequest = getRequestBuilder().vendorConfig(vendorConfig).build();

        String expected = """
                {"FirstName":"john","LastName":"doe","cid":"custom"}""";
        String actual = httpRequestBuilder.build("custom", vendorCallRequest, ConfigFactory.parseMap(configs)).block().getBody();
        assertEquals(expected, actual);
    }

    @Test
    void testTriceCreateParty() throws Exception {
        HttpRequestBuilder httpRequestBuilder = new HttpRequestBuilder(new ObjectMapper(), new RequestTemplateHandler(new ObjectMapper(), new CustomFunctionProvider(customFunctionMap)));
        var configs = new HashMap<String, Object>();
        configs.put("configs.licence.key", "default");
        configs.put("configs.default.header.template", "customheaders");

        Map<String, String> vendorConfig = new HashMap<>();
        vendorConfig.put("template", "trice.body.create-party");
        Piis piis = Piis.builder()
                .firstName("Fname")
                .middleName("Jan")
                .surName("Lname")
                .fullName("FLName")
                .company("CName")
                .mobileNumber("**********")
                .homeNumber("**********")
                .gender("Male")
                .nationalId("************")
                .streetNumber("3")
                .streetName("Nad Stawem")
                .streetAddress("123 Victoria Street")
                .streetAddress2("Random Street")
                .city("Örebro")
                .state("Spain")
                .country("SE")
                .zipCode("70234")
                .dob("********")
                .email("<EMAIL>")
                .userConsent(true)
                .build();
        VendorCallRequest.VendorCallRequestBuilder requestBuilder = VendorCallRequest.builder()
                .vendorName("trice")
                .transactionId("*********-ml-ae")
                .piis(piis);

        VendorCallRequest vendorCallRequest = requestBuilder.vendorConfig(vendorConfig).build();

        String expected = """
                {
                  "address": {
                    "line1": "123 Victoria Street",
                    "line2": "Random Street",
                    "city": "Örebro",
                    "state": "Spain",
                    "country": "SE",
                    "zip": "70234"
                  },
                  "bank_details": {
                    "account_holder_name": "Fname Jan Lname"
                  },
                  "can_receive": true,
                  "name": "Fname Jan Lname",
                  "email": "<EMAIL>",
                  "dob": {
                    "day": 1,
                    "month": 1,
                    "year": 1990
                  }
                }
                """;
        String actual = httpRequestBuilder.build("trice", vendorCallRequest, ConfigFactory.parseMap(configs)).block().getBody();
        compareJsonStrings(expected, actual);
    }

    @Test
    void testTriceCreateRTPTransfer() throws Exception {
        HttpRequestBuilder httpRequestBuilder = new HttpRequestBuilder(new ObjectMapper(), new RequestTemplateHandler(new ObjectMapper(), new CustomFunctionProvider(customFunctionMap)));
        var configs = new HashMap<String, Object>();
        configs.put("configs.licence.key", "default");
        configs.put("configs.default.header.template", "customheaders");
        configs.put("configs.trice.enrollment.id", "customenroll");

        Map<String, String> vendorConfig = new HashMap<>();
        vendorConfig.put("template", "trice.body.create-rtp-transfer");
        vendorConfig.put("memo", "testMemo");
        Piis piis = Piis.builder()
                .firstName("Fname")
                .middleName("Jan")
                .surName("Lname")
                .fullName("FLName")
                .company("CName")
                .mobileNumber("**********")
                .homeNumber("**********")
                .gender("Male")
                .nationalId("************")
                .streetNumber("3")
                .streetName("Nad Stawem")
                .streetAddress("123 Victoria Street")
                .streetAddress2("Random Street")
                .city("Örebro")
                .state("Spain")
                .country("SE")
                .zipCode("70234")
                .dob("********")
                .email("<EMAIL>")
                .userConsent(true)
                .build();


        VendorCallRequest.VendorCallRequestBuilder requestBuilder = VendorCallRequest.builder()
                .vendorName("trice")
                .transactionId("*********-ml-ae")
                .piis(piis);

        VendorCallRequest vendorCallRequest = requestBuilder.vendorConfig(vendorConfig).build();

        String expected = """
                {
                  "amount": 1,
                  "enrollment": "customenroll",
                  "internal_description": "*********-ml-ae",
                  "memo": "testMemo"
                }
                """;
        String actual = httpRequestBuilder.build("trice", vendorCallRequest, ConfigFactory.parseMap(configs)).block().getBody();
        compareJsonStrings(expected, actual);
    }

    @Test
    void testTriceCreateSmartVerifyTransfer() throws Exception {
        HttpRequestBuilder httpRequestBuilder = new HttpRequestBuilder(new ObjectMapper(), new RequestTemplateHandler(new ObjectMapper(), new CustomFunctionProvider(customFunctionMap)));
        var configs = new HashMap<String, Object>();
        configs.put("configs.licence.key", "default");
        configs.put("configs.default.header.template", "customheaders");
        configs.put("configs.trice.enrollment.id", "customenroll");

        Map<String, String> vendorConfig = new HashMap<>();
        vendorConfig.put("template", "trice.body.create-smart-verify-transfer");
        vendorConfig.put("memo", "testMemo");
        Piis piis = Piis.builder()
                .firstName("Fname")
                .middleName("Jan")
                .surName("Lname")
                .fullName("FLName")
                .company("CName")
                .mobileNumber("**********")
                .homeNumber("**********")
                .gender("Male")
                .nationalId("************")
                .streetNumber("3")
                .streetName("Nad Stawem")
                .streetAddress("123 Victoria Street")
                .streetAddress2("Random Street")
                .city("Örebro")
                .state("Spain")
                .country("SE")
                .zipCode("70234")
                .dob("********")
                .email("<EMAIL>")
                .userConsent(true)
                .build();


        VendorCallRequest.VendorCallRequestBuilder requestBuilder = VendorCallRequest.builder()
                .vendorName("trice")
                .transactionId("*********-ml-ae")
                .piis(piis);

        VendorCallRequest vendorCallRequest = requestBuilder.vendorConfig(vendorConfig).build();

        String expected = """
                {
                   "amount": 1,
                   "enrollment": "customenroll",
                   "internal_description": "*********-ml-ae",
                   "memo": "testMemo",
                   "new_party": {
                     "address": {
                       "line1": "123 Victoria Street",
                       "line2": "Random Street",
                       "city": "Örebro",
                       "state": "Spain",
                       "country": "SE",
                       "zip": "70234"
                     },
                     "bank_details": {
                       "account_holder_name": "Fname Jan Lname"
                     },
                     "can_receive": true,
                     "name": "Fname Jan Lname",
                     "email": "<EMAIL>",
                     "dob": {
                       "day": 1,
                       "month": 1,
                       "year": 1990
                     }
                   }
                 }
                """;
        String actual = httpRequestBuilder.build("trice", vendorCallRequest, ConfigFactory.parseMap(configs)).block().getBody();
        compareJsonStrings(expected, actual);
    }

    @Test
    void testTriceCreateUltimateSendingParty() throws Exception {
        HttpRequestBuilder httpRequestBuilder = new HttpRequestBuilder(new ObjectMapper(), new RequestTemplateHandler(new ObjectMapper(), new CustomFunctionProvider(customFunctionMap)));
        var configs = new HashMap<String, Object>();
        configs.put("configs.licence.key", "default");
        configs.put("configs.default.header.template", "customheaders");
        configs.put("configs.trice.enrollment.id", "customenroll");

        Map<String, String> vendorConfig = new HashMap<>();
        vendorConfig.put("template", "trice.body.create-ultimate-sending-party");
        vendorConfig.put("memo", "testMemo");
        Piis piis = Piis.builder()
                .firstName("Fname")
                .middleName("Jan")
                .surName("Lname")
                .fullName("FLName")
                .company("CName")
                .mobileNumber("**********")
                .homeNumber("**********")
                .gender("Male")
                .nationalId("************")
                .streetNumber("3")
                .streetName("Nad Stawem")
                .streetAddress("123 Victoria Street")
                .streetAddress2("Random Street")
                .city("Örebro")
                .state("Spain")
                .country("SE")
                .zipCode("70234")
                .dob("********")
                .email("<EMAIL>")
                .userConsent(true)
                .build();


        VendorCallRequest.VendorCallRequestBuilder requestBuilder = VendorCallRequest.builder()
                .vendorName("trice")
                .transactionId("*********-ml-ae")
                .piis(piis);

        VendorCallRequest vendorCallRequest = requestBuilder.vendorConfig(vendorConfig).build();

        String expected = """
                {
                  "address": {
                    "line1": "123 Victoria Street",
                    "line2": "Random Street",
                    "city": "Örebro",
                    "state": "Spain",
                    "country": "SE",
                    "zip": "70234"
                  },
                  "verified": true,
                  "can_receive": true,
                  "domestic": true,
                  "type": "business",
                  "name": "Fname Jan Lname"
                }
                """;
        String actual = httpRequestBuilder.build("trice", vendorCallRequest, ConfigFactory.parseMap(configs)).block().getBody();
        compareJsonStrings(expected, actual);
    }

    @Test
    void testRetrieveAccount() throws Exception {
        HttpRequestBuilder httpRequestBuilder = new HttpRequestBuilder(new ObjectMapper(), new RequestTemplateHandler(new ObjectMapper(), new CustomFunctionProvider(customFunctionMap)));
        var configs = new HashMap<String, Object>();
        configs.put("configs.licence.key", "default");
        configs.put("configs.default.header.template", "customheaders");
        configs.put("configs.trice.enrollment.id", "customenroll");

        Map<String, String> vendorConfig = new HashMap<>();
        vendorConfig.put("template", "trice.body.retrieve-account");
        vendorConfig.put("memo", "testMemo");
        Piis piis = Piis.builder()
                .firstName("Fname")
                .middleName("Jan")
                .surName("Lname")
                .fullName("FLName")
                .company("CName")
                .mobileNumber("**********")
                .homeNumber("**********")
                .gender("Male")
                .nationalId("************")
                .streetNumber("3")
                .streetName("Nad Stawem")
                .streetAddress("123 Victoria Street")
                .streetAddress2("Random Street")
                .city("Örebro")
                .state("Spain")
                .country("SE")
                .zipCode("70234")
                .dob("********")
                .email("<EMAIL>")
                .userConsent(true)
                .build();


        VendorCallRequest.VendorCallRequestBuilder requestBuilder = VendorCallRequest.builder()
                .vendorName("trice")
                .transactionId("*********-ml-ae")
                .piis(piis);

        VendorCallRequest vendorCallRequest = requestBuilder.vendorConfig(vendorConfig).build();

        String expected = """
                {
                }
                """;
        String actual = httpRequestBuilder.build("trice", vendorCallRequest, ConfigFactory.parseMap(configs)).block().getBody();
        compareJsonStrings(expected, actual);
    }

    private void compareJsonStrings(String expected, String actual) throws Exception {
        // Parse the JSON strings to JSON objects
        Object expectedJson = OBJECT_MAPPER.readValue(expected, Object.class);
        Object actualJson = OBJECT_MAPPER.readValue(actual, Object.class);

        // Compare the JSON objects
        assertEquals(expectedJson, actualJson);
    }

    private VendorCallRequest.VendorCallRequestBuilder getRequestBuilder() {
        Piis piis = Piis.builder()
                .firstName("john")
                .surName("doe")
                .city("ny")
                .country("us")
                .build();
        return VendorCallRequest.builder()
                .vendorName("custom")
                .piis(piis);
    }


}
