////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2024 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.builder.response;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.databind.ObjectMapper;
import me.socure.ai.vendor.core.model.VendorCallResponse;
import org.junit.jupiter.api.Test;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import reactor.core.publisher.Mono;

/**
 * Unit tests for HTTP response builder.
 */
public class HttpResponseBuilderTest {

    @Test
    public void testBuild() {
        // Mock data
        String vendorName = "mockVendor";

        Mono<VendorCallResponse<?>> response = Mono.just(VendorCallResponse.builder().response("Mock response").build());
        Config defaultVendorConfig = ConfigFactory.empty();

        // Create an instance of HttpResponseBuilder
        HttpResponseBuilder httpResponseBuilder = new HttpResponseBuilder(new ObjectMapper());

        // Invoke the build method
        Mono<VendorCallResponse<?>> builtResponse = httpResponseBuilder.build(vendorName, response, defaultVendorConfig);

        // Assert the response is not null and remains unchanged
        assertEquals(response.block(), builtResponse.block());
    }

}
