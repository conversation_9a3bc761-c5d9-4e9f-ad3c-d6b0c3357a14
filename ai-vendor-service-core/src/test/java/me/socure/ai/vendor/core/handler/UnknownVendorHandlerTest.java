////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2024 Socure Inc.
// All rights reserved.
////////////////////////////////////////////////////////////////////////////////

package me.socure.ai.vendor.core.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.Collections;
import java.util.Map;

import me.socure.ai.vendor.core.model.VendorCallResponse;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

/**
 * Unit tests for the UnknownVendorHandler class.
 */
public class UnknownVendorHandlerTest {

    private final UnknownVendorHandler unknownVendorHandler = new UnknownVendorHandler();

    @Test
    void processRequest_ReturnsErrorResponse_WhenVendorNameIsUnknown() {
        String unknownVendorName = "UnknownVendor";
        String request = "some request data";
        Map<String, String> requestConfigs = Collections.emptyMap();

        VendorCallResponse<?> response = unknownVendorHandler.processRequest(unknownVendorName, request, requestConfigs, "", -1L).block();

        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals(unknownVendorName, response.getVendorName());
        assertEquals("Unknown vendor name: " + unknownVendorName, response.getResponse());
    }

    @Test
    void processRequest_ReturnsErrorResponse_WhenVendorNameIsNull() {
        String nullVendorName = null;
        String request = "some request data";
        Map<String, String> requestConfigs = Collections.emptyMap();

        VendorCallResponse<?> response = unknownVendorHandler.processRequest(nullVendorName, request, requestConfigs, "", -1L).block();

        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals(nullVendorName, response.getVendorName());
        assertEquals("Unknown vendor name: null", response.getResponse());
    }

}
