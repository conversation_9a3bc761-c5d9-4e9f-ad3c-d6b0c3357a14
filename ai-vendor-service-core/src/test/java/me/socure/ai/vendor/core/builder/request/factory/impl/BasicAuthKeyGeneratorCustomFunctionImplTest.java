package me.socure.ai.vendor.core.builder.request.factory.impl;

import me.socure.ai.vendor.core.builder.request.model.CacheKey;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Arrays;
import java.util.Base64;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;

public class BasicAuthKeyGeneratorCustomFunctionImplTest {

	CacheableAuthKeyGeneratorCustomFunctionImpl basicAuthKeyGeneratorCustomFunctionImpl = new BasicAuthKeyGeneratorCustomFunctionImpl();

	Config config = ConfigFactory.empty();

	@Test
	public void testGetAuthValue() {
		String expectedAuthValue = "Basic " + Base64.getEncoder().encodeToString("UserName:PassWord".getBytes());

		List<String> inputElements = Arrays.asList("basic", "triceAuthKey", "-1", "UserName", "PassWord");
		String actualAuthValue = (String) basicAuthKeyGeneratorCustomFunctionImpl.executeCustomFunction(inputElements, config);

		CacheKey searchKey = CacheKey.builder().cacheKeyName("triceAuthKey").build();
		String authValueFromCache = basicAuthKeyGeneratorCustomFunctionImpl.getAuthKeysCache().get(searchKey);

		assertEquals(expectedAuthValue, actualAuthValue);
		assertEquals(expectedAuthValue, authValueFromCache);
	}

	@Test
	public void testGetAuthValueWithDifferentCredentials() {
		List<String> inputElements = Arrays.asList("basic", "triceAuthKey", "-1", "user", "pass");
		String expectedAuthValue = "Basic " + Base64.getEncoder().encodeToString("user:pass".getBytes());

		String actualAuthValue = (String) basicAuthKeyGeneratorCustomFunctionImpl.executeCustomFunction(inputElements, config);

		CacheKey searchKey = CacheKey.builder().cacheKeyName("triceAuthKey").build();
		String authValueFromCache = basicAuthKeyGeneratorCustomFunctionImpl.getAuthKeysCache().get(searchKey);

		assertEquals(expectedAuthValue, actualAuthValue);
		assertEquals(expectedAuthValue, authValueFromCache);
	}
}
